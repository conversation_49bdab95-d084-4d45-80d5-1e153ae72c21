"""RAGFlow Agent implementation for querying private knowledge base."""

import os
from google.adk.agents import LlmAgent
from mareo.common.llm.ragflow_client import get_ragflow_client
from mareo.common.tools.rest_api.ragflow_tool import query_ragflow_knowledge


def create_ragflow_agent() -> LlmAgent:
    """Factory method to create a new RAGFlow agent instance."""
    
    # Use RAGFlow client with direct API integration for better knowledge access
    model = get_ragflow_client()
    
    # Check if RAGFlow configuration is available
    ragflow_token = os.getenv('RAGFLOW_AUTH_TOKEN', 'ragflow-M4NjMyYmIyOTc2YjExZjBhODAwMDAxNj')
    
    instruction = """You are a RAGFlow knowledge agent specialized in accessing private knowledge bases.

Your primary function is to:
1. Query the RAGFlow knowledge base using the query_ragflow_knowledge tool
2. Provide accurate, well-sourced answers based on the private knowledge retrieved
3. Always cite the source of information when available
4. If the knowledge base doesn't contain relevant information, clearly state this limitation

Guidelines:
- Use the RAGFlow tool to search for information related to the user's question
- Present the information in a clear, structured format
- Maintain accuracy and avoid hallucination - only use information from the knowledge base
- If multiple pieces of information are relevant, organize them logically
- After getting the information, transfer back to the parent agent with the retrieved knowledge

Remember: You are accessing private, curated knowledge that may not be available in general training data."""

    return LlmAgent(
        name='ragflow_knowledge_agent',
        model=model,
        description='Agent to query RAGFlow private knowledge base and provide sourced answers',
        instruction=instruction,
        tools=[query_ragflow_knowledge] if ragflow_token else [],
    )


# Create the root agent instance
root_agent = create_ragflow_agent()