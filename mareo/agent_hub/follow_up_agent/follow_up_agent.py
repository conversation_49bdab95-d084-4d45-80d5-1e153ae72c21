"""
Follow-up Agent Module

This module implements a specialized agent that generates follow-up question suggestions
based on the conversation context. It helps users continue the conversation by providing
relevant questions they might want to ask next.
"""

import json
from typing import Any, AsyncGenerator

from google.adk.agents import InvocationContext
from google.adk.agents.llm_agent import LlmAgent
from google.adk.agents.run_config import StreamingMode
from google.adk.events import Event
from google.adk.models.lite_llm import Li<PERSON><PERSON><PERSON>
from google.adk.tools import BaseTool, ToolContext
from google.genai import types


class FollowUpAgent(LlmAgent):
    """
    Follow-up suggestion agent that generates contextual follow-up questions.

    This agent analyzes the conversation context and generates three relevant
    follow-up questions that users might want to ask next. It uses the Gemini
    model to understand context and provide intelligent suggestions in the
    user's language.
    """

    async def _run_async_impl(self, ctx: InvocationContext) -> AsyncGenerator[Event, None]:
        def make_follow_up_suggestions(suggestion1: str, suggestion2: str, suggestion3: str):
            """
            Tool function for generating follow-up suggestions.

            This function is called by the LLM to structure its output as three
            distinct follow-up questions.

            Args:
                suggestion1 (str): First follow-up suggestion
                suggestion2 (str): Second follow-up suggestion
                suggestion3 (str): Third follow-up suggestion

            Returns:
                list[str]: List of the three suggestions
            """
            return [suggestion1, suggestion2, suggestion3]

        async def after_tool_callback(
            tool: BaseTool,
            args: dict[str, Any],
            tool_context: ToolContext,
            tool_response: dict,
        ) -> dict | None:
            """
            Callback executed after tool invocation.

            This callback ensures that the tool response is not summarized,
            preserving the exact follow-up suggestions generated.

            Args:
                tool (BaseTool): The tool that was executed
                args (dict[str, Any]): Arguments passed to the tool
                tool_context (ToolContext): Context of the tool execution
                tool_response (dict): Response from the tool

            Returns:
                dict | None: The unmodified tool response
            """
            tool_context.actions.skip_summarization = True
            return None

        # Create internal LLM agent for generating follow-up suggestions
        _follow_up_agent = LlmAgent(
            name='follow_up_agent',
            model=LiteLlm(
                'openrouter/google/gemini-2.5-flash',
                tool_choice='required',
            ),  # Use Gemini 2.5 Flash model
            instruction='Generate 3 possible follow-ups as the user in their language based on the context.',
            tools=[make_follow_up_suggestions],  # Provide the suggestion tool
            after_tool_callback=after_tool_callback,  # Skip summarization callback
        )

        # Disable streaming mode for this agent to get complete results
        ctx.run_config.streaming_mode = StreamingMode.NONE

        # Run the internal agent and collect follow-up suggestions
        agen = _follow_up_agent.run_async(ctx)
        follow_ups = []
        async for event in agen:
            # Extract follow-up suggestions from the function call arguments
            follow_ups = follow_ups if follow_ups else list(event.content.parts[0].function_call.args.values())

        # Create event containing the follow-up suggestions as JSON data
        event = Event(
            content=types.Content(
                parts=[
                    types.Part(
                        inline_data=types.Blob(
                            display_name='follow_ups', data=bytes(json.dumps(follow_ups), encoding='utf-8')
                        )
                    )
                ]
            ),
            author='follow_ups',  # Mark the author as the follow-ups system
            invocation_id=ctx.invocation_id,
            branch=ctx.branch + '.'
            if ctx.branch
            else '' + '_FollowUpAgent',  # Mark as a different branch to avoid polluting context
        )
        yield event


def create_follow_up_agent() -> FollowUpAgent:
    """Factory method to create a new follow-up agent instance."""
    return FollowUpAgent(name='follow_up')


# Create the root follow-up agent instance
root_agent = create_follow_up_agent()
