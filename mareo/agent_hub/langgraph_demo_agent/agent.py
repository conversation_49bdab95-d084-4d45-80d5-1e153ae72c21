from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph

from mareo.common.agents.LangGraphWrapperAgent import LangGraphWrapperAgent


def create_langgraph_demo_agent() -> LangGraphWrapperAgent:
    """Factory method to create a new langgraph demo agent instance."""
    # 1. 定义你的 StateGraph
    g = StateGraph(dict)
    g.add_node('echo', lambda s: {'output': f'ACK: {s["input"]}'})
    g.add_edge(START, 'echo')
    g.add_edge('echo', END)

    # 2. 编译 + 带 Checkpointer
    compiled = g.compile(checkpointer=MemorySaver())

    # 3. 包成 ADK Agent
    return LangGraphWrapperAgent(name='echo_agent', compiled_graph=compiled)


root_agent = create_langgraph_demo_agent()
