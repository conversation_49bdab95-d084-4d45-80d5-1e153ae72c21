from google.adk.agents import LlmAgent

from mareo.agent_hub.html_artifact_agent.agent import save_webpage_html
from mareo.common.llm.predefined.claude_sonnet4 import claude_sonnet4
from mareo.common.tools.mcp.supabase import get_supabase_toolset


def create_supabase_web_app_agent() -> LlmAgent:
    """Factory to create a sub-agent that builds a minimal Supabase-backed web app page.

    Behavior:
    - On transfer, call `build_supabase_demo_page` once to generate and save the page artifact.
    - Then transfer immediately back to the parent agent.
    """
    instruction = (
        'You are a web app builder using Supabase via MCP. '
        'On transfer, do the following via tools: '
        '1) Use Supabase MCP tools to create necessary tables, insert a sample row if empty, and select up to 10 rows. '
        '2) Build a single-page HTML web app using the database as backend, create the page by calling `save_webpage_html`. '
        # 'After saving, transfer back to your parent agent immediately. Always explain what you are doing before calling a tool.'
    )

    return LlmAgent(
        name='supabase_web_app_agent',
        model=claude_sonnet4,
        description='Agent to build a Supabase-backed web app page via MCP and save as artifact',
        instruction=instruction,
        tools=[get_supabase_toolset(), save_webpage_html],
    )


root_agent = create_supabase_web_app_agent()
