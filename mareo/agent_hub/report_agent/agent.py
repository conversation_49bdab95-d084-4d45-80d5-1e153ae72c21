from google.adk.agents import LlmAgent

from mareo.common.llm.predefined.gemini_2_5_pro import get_gemini_2_5_pro
from mareo.common.models.report_schema import Statement
from mareo.common.plugins import register_agent_plugins
from mareo.common.plugins.inject_attachment_plugin import InjectAttachmentPlugin

register_agent_plugins(
    'report_agent',
    [InjectAttachmentPlugin(name='inject_attachment_plugin')],
    replace=True,
)


def create_report_agent() -> LlmAgent:
    """Factory method to create a new report agent instance."""

    model = get_gemini_2_5_pro(
        response_format=Statement,
        structured_outputs=True,
    )

    return LlmAgent(
        name='report_agent',
        model=model,
        description='Agent to generate trackable report',
        instruction="""你是一个可溯源报告生成器。
只允许使用“证据池”片段来支持事实类主张（L1/L2/L3）。
输出必须是合法 JSON，结构见Schema，不得输出任何额外文本。

生成规则
	1.	最小单元：statements 生成。每个 statement 必须包含 claims[]（至少 1 条）。
	2.	主张分层（level）：
	•	L3 引文：直接引用原文；
	•	L2 释义事实：忠实改写，有证据；
	•	L1 推导：基于本次输出中你已经写出的 L2/L3 做计算或逻辑组合；
	•	L0 洞见/缺口：无直接证据的判断或“材料未提到”，必须给 reasoning 与 validation_needed。
	3.	证据引用（Facts 专用）：
	•	只写 { "source_id": "Sx", "span_id": "..." }；不得编造坐标或粘贴长摘。
	•	数字原样或等值单位表达（%, bps 等）的一致性优先；必要时复述短语，但不得歪曲。
	•	每条事实主张建议 1–2 个证据片段即可，避免过多堆砌。
	4.	L1 的依据引用：
	•	derived_from.basis_claims_inline 里的 ref 必须是你刚才在本次输出里写过的 L2/L3 claim 的简短复述（≤32 字），用于后续绑定。不得引用未出现过的内容。
	5.	写作风格：中文，简洁准确；每个 statement.text 建议 ≤120 字。
	6.	限制：
	•	Facts（L1/L2/L3）必须有证据；无证据就归为 L0。
	•	只用证据池中给出的片段；不得引用外部常识或来源。
	7.	输出：仅按下方 Schema 返回 JSON，不允许额外解释或 Markdown。
        """,
        # 'You have specialized sub-agents: '
        # "1. 'Mareo_search': Handles online searching. Delegate to it for these. "
        # "Analyze the user's query. If it might require searching, delegate to 'Mareo_search'. Then respond with medium length content with the information returned. "
        # "If it's just chat, handle it yourself. "
        # 'For anything else, respond appropriately or state you cannot handle it.'
        # planner=MareoPlanner(thinking_config=ThinkingConfig(include_thoughts=True)),
    )


# def inject_report(callback_context: CallbackContext, llm_request: LlmRequest) -> LlmResponse | None:
#     # Inject data only into the first user message
#     if llm_request.contents and len(llm_request.contents) == 1 and llm_request.contents[-1].role == 'user':
#         resp = httpx.get('https://asset.mareo.ai/data.txt')
#         data = resp.content.decode('utf-8')
#         original_user_msg = llm_request.contents[-1].parts[0].text
#         llm_request.contents[-1].parts[0].text = f'这是所有的证据池片段：\n\n{data}\n\n{original_user_msg}'
#         callback_context.user_content.parts[0].text = f'这是所有的证据池片段：\n\n{data}\n\n{original_user_msg}'
#     return None


# Create agent with tools - tools will be created in proper async context when needed
root_agent = create_report_agent()
