from typing import Optional

from google.adk.agents import LlmAgent
from google.adk.tools.tool_context import ToolContext
from google.genai import types

from mareo.common.llm.predefined.claude_sonnet4 import get_claude_sonnet4


def _build_html_document(title: str, body_html: str, css: Optional[str] = None, js: Optional[str] = None) -> str:
    """Create a minimal HTML document string."""
    head = f"<meta charset='utf-8'>\n<title>{title}</title>"
    if css:
        head += f'\n<style>\n{css}\n</style>'
    script_str = f'\n<script>\n{js}\n</script>' if js else ''
    return f"<!doctype html>\n<html lang='en'>\n<head>\n{head}\n</head>\n<body>\n{body_html}{script_str}\n</body>\n</html>\n"


async def save_webpage_html(
    tool_context: ToolContext,
    filename: str,
    title: str,
    body_html: str,
    css: Optional[str] = None,
    js: Optional[str] = None,
) -> dict:
    """Build an HTML page and save it as an artifact based on the parameters.

    Args:
        filename: Target artifact filename. Should end with `.html`.
        title: HTML document title. Inserted into the <title> tag.
        body_html: Raw HTML string inserted into the <body> section.
        css: Optional CSS string to inline in a <style> tag in the <head>.
        js: Optional JavaScript string to inline in a <script> tag before </body>.

    Returns:
        dict: A summary with keys:
            - status: Literal string "saved" on success.
            - filename: The artifact filename used.
            - version: Integer artifact version returned by the service.
    """
    html = _build_html_document(title=title, body_html=body_html, css=css, js=js)
    html_part = types.Part.from_bytes(data=html.encode('utf-8'), mime_type='text/html')
    version = await tool_context.save_artifact(filename=filename, artifact=html_part)
    return {
        'status': 'saved, user is able to see the link, do not try to provide link to the user.',
        'filename': filename,
        'version': int(version),
    }


def create_html_artifact_agent() -> LlmAgent:
    """Factory to create an agent that builds webpages and saves them as artifacts."""
    instruction = (
        'You are a web app builder, you build modern web apps. When the conversation is transferred to you, '
        'always call the tool `save_webpage_html` with a concise filename ending with .html, '
        'a clear title, HTML, and CSS/JS to form a well-structured, comprehensive, and visually appealing web app. '
        "If no filename is given, choose a reasonable default such as 'page.html'. "
        'After confirming the web page is saved, directly transfer back to your parent agent.'
    )

    model = get_claude_sonnet4(tool_choice='required')
    return LlmAgent(
        name='web_page_agent',
        model=model,
        description='Agent to create HTML pages and save them as artifacts',
        instruction=instruction,
        tools=[save_webpage_html],
    )


# Root instance following the project convention
root_agent = create_html_artifact_agent()
