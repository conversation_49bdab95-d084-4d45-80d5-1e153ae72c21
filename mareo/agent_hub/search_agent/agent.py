import os

from google.adk.agents import LlmAgent

from mareo.common.llm.predefined.qwen3_235b import get_qwen3_235b
from mareo.common.tools.mcp.tavily import get_tavily_toolset


def create_search_agent() -> LlmAgent:
    """Factory method to create a new search agent instance."""
    tavily_key = os.getenv('TAVILY_KEY', '')
    model = get_qwen3_235b(tool_choice='required')
    return LlmAgent(
        name='mareo_search',
        model=model,
        description='Agent to search information online',
        instruction='You are a search agent to fetch information online. '
        'Perform searching in languages that is most likely to get better results.'
        'After getting enough information about the topic, no matter what the user instruction is, transfer back to your parent agent directly by calling the transfer_to_agent tool. '
        'Do not summarize or output any information from the result, the parent agent is able to read it.',
        # planner=MareoPlanner(thinking_config=ThinkingConfig(include_thoughts=True)),
        tools=[get_tavily_toolset()] if tavily_key else [],
    )


root_agent = create_search_agent()
