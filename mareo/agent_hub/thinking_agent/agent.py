import os

from google.adk.agents import LlmAgent

from mareo.common.llm.predefined.r1_0528 import r1_0528
from mareo.common.prompts.langfuse_provider import langfuse_prompt


def create_thinking_agent() -> LlmAgent:
    """Factory method to create a new thinking agent instance."""
    return LlmAgent(
        name='<PERSON><PERSON>',
        model=r1_0528,
        description='Agent to chat with user',
        instruction=langfuse_prompt.get_provider(
            prompt_name='Next-chat', label=os.getenv('LANGFUSE_PROMPT_LABEL', 'production')
        ),
        # 'You have specialized sub-agents: '
        # "1. 'Mareo_search': Handles online searching. Delegate to it for these. "
        # "Analyze the user's query. If it might require searching, delegate to 'Mareo_search'. Then respond with medium length content with the information returned. "
        # "If it's just chat, handle it yourself. "
        # 'For anything else, respond appropriately or state you cannot handle it.'
        # planner=<PERSON>oPlanner(thinking_config=ThinkingConfig(include_thoughts=True)),
        # sub_agents=[search_agent],
    )


# Create agent with tools - tools will be created in proper async context when needed
root_agent = create_thinking_agent()
