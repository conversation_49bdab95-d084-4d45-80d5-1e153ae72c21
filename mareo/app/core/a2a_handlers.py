from __future__ import annotations

import json
import logging
from pathlib import Path

from fastapi import FastAPI, Request
from google.adk import Runner

from mareo.app.api.v1.endpoints.agent import get_runner_async

logger = logging.getLogger('google_adk.' + __name__)


def setup_a2a(app: FastAPI):
    try:
        from a2a.server.apps import A2AStarletteApplication
        from a2a.server.request_handlers import De<PERSON>ultRequestHandler
        from a2a.server.tasks import InMemoryTaskStore
        from a2a.types import AgentCard
        from a2a.utils.constants import AGENT_CARD_WELL_KNOWN_PATH
        from google.adk.a2a.executor.a2a_agent_executor import A2aAgentExecutor

    except ImportError as e:
        import sys

        if sys.version_info < (3, 10):
            raise ImportError('A2A requires Python 3.10 or above. Please upgrade your Python version.') from e
        else:
            raise e

    base_path = Path.cwd() / app.state.agents_dir
    if base_path.exists() and base_path.is_dir():
        a2a_task_store = InMemoryTaskStore()

        def create_a2a_runner_loader(captured_app_name: str):
            """Factory function to create A2A runner with proper closure."""

            async def _get_a2a_runner_async(request: Request) -> Runner:
                return await get_runner_async(
                    captured_app_name,
                    request,
                )

            return _get_a2a_runner_async

        for p in base_path.iterdir():
            if p.is_file() or p.name.startswith(('.', '__pycache__')) or not (p / 'agent.json').is_file():
                continue

            app_name = p.name
            logger.info('Setting up A2A agent: %s', app_name)

            try:
                a2a_rpc_path = f'http://{app.state.host}:{app.state.port}/a2a/{app_name}'

                agent_executor = A2aAgentExecutor(
                    runner=create_a2a_runner_loader(app_name),
                )

                request_handler = DefaultRequestHandler(agent_executor=agent_executor, task_store=a2a_task_store)

                with (p / 'agent.json').open('r', encoding='utf-8') as f:
                    data = json.load(f)
                    agent_card = AgentCard(**data)
                    agent_card.url = a2a_rpc_path

                a2a_app = A2AStarletteApplication(
                    agent_card=agent_card,
                    http_handler=request_handler,
                )

                routes = a2a_app.routes(
                    rpc_url=f'/a2a/{app_name}',
                    agent_card_url=f'/a2a/{app_name}{AGENT_CARD_WELL_KNOWN_PATH}',
                )

                for new_route in routes:
                    app.router.routes.append(new_route)

                logger.info('Successfully configured A2A agent: %s', app_name)

            except Exception as e:
                logger.error('Failed to setup A2A agent %s: %s', app_name, e)
