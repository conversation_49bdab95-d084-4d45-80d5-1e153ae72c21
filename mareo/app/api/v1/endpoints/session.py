from __future__ import annotations

import json
import logging
from datetime import datetime, timezone
from pathlib import Path
from typing import Annotated, Any, Optional

import graphviz
from fastapi import APIRouter, File, HTTPException, Query, Request, UploadFile
from google.adk.cli import agent_graph
from google.adk.events.event import Event
from google.adk.sessions.session import Session
from google.genai import types

from mareo.app.models.api_models import GetEventGraphResult
from mareo.common.file_processors.pdf_json import aggregate_markdown_lines_from_pdf_json

router = APIRouter()
logger = logging.getLogger('google_adk.' + __name__)

EVAL_SESSION_ID_PREFIX = 'eval-'


@router.get(
    '/apps/{app_name}/users/{user_id}/sessions/{session_id}',
    response_model_exclude_none=True,
)
async def get_session(app_name: str, user_id: str, session_id: str, request: Request) -> Session:
    session_service = request.app.state.session_service
    session = await session_service.get_session(app_name=app_name, user_id=user_id, session_id=session_id)
    if not session:
        raise HTTPException(status_code=404, detail='Session not found')
    return session


@router.get(
    '/apps/{app_name}/users/{user_id}/sessions',
    response_model_exclude_none=True,
)
async def list_sessions(app_name: str, user_id: str, request: Request) -> list[Session]:
    session_service = request.app.state.session_service
    list_sessions_response = await session_service.list_sessions(app_name=app_name, user_id=user_id)
    return [session for session in list_sessions_response.sessions if not session.id.startswith(EVAL_SESSION_ID_PREFIX)]


@router.post(
    '/apps/{app_name}/users/{user_id}/sessions/{session_id}',
    response_model_exclude_none=True,
)
async def create_session_with_id(
    app_name: str,
    user_id: str,
    session_id: str,
    request: Request,
    state: Optional[dict[str, Any]] = None,
) -> Session:
    session_service = request.app.state.session_service
    if await session_service.get_session(app_name=app_name, user_id=user_id, session_id=session_id) is not None:
        logger.warning('Session already exists: %s', session_id)
        raise HTTPException(status_code=400, detail=f'Session already exists: {session_id}')
    logger.info('New session created: %s', session_id)
    return await session_service.create_session(app_name=app_name, user_id=user_id, state=state, session_id=session_id)


@router.post(
    '/apps/{app_name}/users/{user_id}/sessions',
    response_model_exclude_none=True,
)
async def create_session(
    app_name: str,
    user_id: str,
    request: Request,
    state: Optional[dict[str, Any]] = None,
    events: Optional[list[Event]] = None,
) -> Session:
    session_service = request.app.state.session_service
    logger.info('New session created')
    session = await session_service.create_session(app_name=app_name, user_id=user_id, state=state)

    if events:
        for event in events:
            await session_service.append_event(session=session, event=event)

    return session


@router.delete('/apps/{app_name}/users/{user_id}/sessions/{session_id}')
async def delete_session(app_name: str, user_id: str, session_id: str, request: Request):
    session_service = request.app.state.session_service
    await session_service.delete_session(app_name=app_name, user_id=user_id, session_id=session_id)


@router.get(
    '/apps/{app_name}/users/{user_id}/sessions/{session_id}/artifacts/{artifact_name:path}',
    response_model_exclude_none=True,
)
async def load_artifact(
    app_name: str,
    user_id: str,
    session_id: str,
    artifact_name: str,
    request: Request,
    version: Optional[int] = Query(None),
) -> Optional[types.Part]:
    artifact_service = request.app.state.artifact_service
    artifact = await artifact_service.load_artifact(
        app_name=app_name,
        user_id=user_id,
        session_id=session_id,
        filename=artifact_name,
        version=version,
    )
    if not artifact:
        raise HTTPException(status_code=404, detail='Artifact not found')
    return artifact


@router.get(
    '/apps/{app_name}/users/{user_id}/sessions/{session_id}/artifacts/{artifact_name}/versions/{version_id}',
    response_model_exclude_none=True,
)
async def load_artifact_version(
    app_name: str,
    user_id: str,
    session_id: str,
    artifact_name: str,
    version_id: int,
    request: Request,
) -> Optional[types.Part]:
    artifact_service = request.app.state.artifact_service
    artifact = await artifact_service.load_artifact(
        app_name=app_name,
        user_id=user_id,
        session_id=session_id,
        filename=artifact_name,
        version=version_id,
    )
    if not artifact:
        raise HTTPException(status_code=404, detail='Artifact not found')
    return artifact


@router.get(
    '/apps/{app_name}/users/{user_id}/sessions/{session_id}/artifacts',
    response_model_exclude_none=True,
)
async def list_artifact_names(app_name: str, user_id: str, session_id: str, request: Request) -> list[str]:
    artifact_service = request.app.state.artifact_service
    return await artifact_service.list_artifact_keys(app_name=app_name, user_id=user_id, session_id=session_id)


@router.get(
    '/apps/{app_name}/users/{user_id}/sessions/{session_id}/artifacts/{artifact_name}/versions',
    response_model_exclude_none=True,
)
async def list_artifact_versions(
    app_name: str,
    user_id: str,
    session_id: str,
    artifact_name: str,
    request: Request,
) -> list[int]:
    artifact_service = request.app.state.artifact_service
    return await artifact_service.list_versions(
        app_name=app_name,
        user_id=user_id,
        session_id=session_id,
        filename=artifact_name,
    )


@router.delete(
    '/apps/{app_name}/users/{user_id}/sessions/{session_id}/artifacts/{artifact_name}',
)
async def delete_artifact(
    app_name: str,
    user_id: str,
    session_id: str,
    artifact_name: str,
    request: Request,
):
    artifact_service = request.app.state.artifact_service
    await artifact_service.delete_artifact(
        app_name=app_name,
        user_id=user_id,
        session_id=session_id,
        filename=artifact_name,
    )


@router.get(
    '/apps/{app_name}/users/{user_id}/sessions/{session_id}/events/{event_id}/graph',
    response_model_exclude_none=True,
)
async def get_event_graph(
    app_name: str, user_id: str, session_id: str, event_id: str, request: Request
) -> GetEventGraphResult | dict:
    session_service = request.app.state.session_service
    session = await session_service.get_session(app_name=app_name, user_id=user_id, session_id=session_id)
    if not session:
        raise HTTPException(status_code=404, detail='Session not found')

    event = next((e for e in session.events if e.id == event_id), None)
    if not event:
        raise HTTPException(status_code=404, detail='Event not found')

    agent_loader = request.app.state.agent_loader
    root_agent = agent_loader.load_agent(app_name)
    dot_graph = None

    function_calls = event.get_function_calls()
    if function_calls:
        highlights = [(event.author, fc.name) for fc in function_calls]
        dot_graph = await agent_graph.get_agent_graph(root_agent, highlights)
    else:
        function_responses = event.get_function_responses()
        if function_responses:
            highlights = [(fr.name, event.author) for fr in function_responses]
            dot_graph = await agent_graph.get_agent_graph(root_agent, highlights)
        else:
            dot_graph = await agent_graph.get_agent_graph(root_agent, [(event.author, '')])

    if dot_graph and isinstance(dot_graph, graphviz.Digraph):
        return GetEventGraphResult(dot_src=dot_graph.source)

    return {}


# =====================
# Evidence upload & pool aggregation
# =====================


def _now_iso() -> str:
    return datetime.now(tz=timezone.utc).isoformat()


def _safe_basename(filename: str) -> str:
    # Normalize and drop any directory components the client may send
    return Path(filename).name


async def _load_manifest(artifact_service, app_name: str, user_id: str, session_id: str) -> dict[str, Any] | None:
    part = await artifact_service.load_artifact(
        app_name=app_name,
        user_id=user_id,
        session_id=session_id,
        filename='evidence/manifest.json',
    )
    if not part:
        return None
    if part.text:
        return json.loads(part.text)
    if part.inline_data and part.inline_data.data:
        return json.loads(part.inline_data.data.decode('utf-8'))
    return None


async def _save_manifest(
    artifact_service,
    app_name: str,
    user_id: str,
    session_id: str,
    manifest: dict[str, Any],
) -> int:
    payload = json.dumps(manifest, ensure_ascii=False).encode('utf-8')
    version = await artifact_service.save_artifact(
        app_name=app_name,
        user_id=user_id,
        session_id=session_id,
        filename='evidence/manifest.json',
        artifact=types.Part.from_bytes(data=payload, mime_type='application/json'),
    )
    return int(version)


async def _rebuild_pool(
    artifact_service,
    app_name: str,
    user_id: str,
    session_id: str,
    manifest: dict[str, Any],
) -> tuple[int, int]:
    """Rebuild evidence pool artifacts (pool.md, pool.json) from current manifest.

    Returns (pool_md_version, pool_json_version).
    """
    sources = manifest.get('sources') or []
    lines: list[str] = []
    for src in sorted(sources, key=lambda x: x.get('short_id', '')):
        short_id = src.get('short_id')
        filename = src.get('filename')
        if not short_id or not filename:
            continue
        part = await artifact_service.load_artifact(
            app_name=app_name,
            user_id=user_id,
            session_id=session_id,
            filename=f'evidence/raw/{filename}',
        )
        if not part:
            continue
        if part.text:
            data = json.loads(part.text)
        elif part.inline_data and part.inline_data.data:
            data = json.loads(part.inline_data.data.decode('utf-8'))
        else:
            continue
        src_lines = aggregate_markdown_lines_from_pdf_json(data, file_tag=short_id)
        lines.extend(src_lines)

    pool_md = ('\n'.join(lines)).encode('utf-8')
    pool_json = json.dumps(lines, ensure_ascii=False, indent=None).encode('utf-8')

    md_version = await artifact_service.save_artifact(
        app_name=app_name,
        user_id=user_id,
        session_id=session_id,
        filename='evidence/pool.md',
        artifact=types.Part.from_bytes(data=pool_md, mime_type='text/markdown'),
    )
    json_version = await artifact_service.save_artifact(
        app_name=app_name,
        user_id=user_id,
        session_id=session_id,
        filename='evidence/pool.json',
        artifact=types.Part.from_bytes(data=pool_json, mime_type='application/json'),
    )
    manifest['pool'] = {
        'latest_version': int(md_version),
        'updated_at': _now_iso(),
    }
    return int(md_version), int(json_version)


@router.post(
    '/apps/{app_name}/users/{user_id}/sessions/{session_id}/evidence/upload',
    response_model_exclude_none=True,
)
@router.post(
    '/apps/{app_name}/users/{user_id}/sessions/{session_id}/evidence:upload',
    response_model_exclude_none=True,
)
async def upload_evidence(
    app_name: str,
    user_id: str,
    session_id: str,
    request: Request,
    files: Annotated[list[UploadFile], File()],
    mode: Annotated[str, Query(pattern='^(append|replace)$')] = 'append',
) -> dict[str, Any]:
    """Upload one or more JSON files, store as raw artifacts, and rebuild the evidence pool.

    - Stable short IDs (S1/S2/...) are assigned per filename and stored in manifest.
    - If `mode=replace`, the manifest is reset and IDs are reassigned from S1.
    """
    session_service = request.app.state.session_service
    session = await session_service.get_session(app_name=app_name, user_id=user_id, session_id=session_id)
    if not session:
        # Auto-create session if it does not exist
        session = await session_service.create_session(
            app_name=app_name, user_id=user_id, state={}, session_id=session_id
        )

    artifact_service = request.app.state.artifact_service

    manifest: dict[str, Any] | None = None
    if mode == 'append':
        manifest = await _load_manifest(artifact_service, app_name, user_id, session_id)
    if not manifest or mode == 'replace':
        manifest = {'sources': [], 'next_index': 1, 'pool': None}

    # Build filename -> short_id map from manifest
    filename_to_id: dict[str, str] = {s['filename']: s['short_id'] for s in manifest['sources']}

    # Save each file as raw artifact and ensure short_id exists
    for f in files:
        base = _safe_basename(f.filename or '')
        if not base:
            raise HTTPException(status_code=400, detail='Empty filename')
        try:
            content_bytes = await f.read()
            # Validate JSON structure
            _ = json.loads(content_bytes.decode('utf-8'))
        except Exception:
            raise HTTPException(status_code=400, detail=f'Invalid JSON: {base}')

        # Assign or reuse short id
        if base in filename_to_id:
            short_id = filename_to_id[base]
        else:
            short_id = f'S{manifest["next_index"]}'
            manifest['next_index'] += 1
            manifest['sources'].append(
                {
                    'short_id': short_id,
                    'filename': base,
                    'latest_version': None,
                    'mime': 'application/json',
                    'updated_at': _now_iso(),
                }
            )
            filename_to_id[base] = short_id

        # Save raw JSON artifact (latest version)
        version = await artifact_service.save_artifact(
            app_name=app_name,
            user_id=user_id,
            session_id=session_id,
            filename=f'evidence/raw/{base}',
            artifact=types.Part.from_bytes(data=content_bytes, mime_type='application/json'),
        )
        # Update latest_version
        for s in manifest['sources']:
            if s['filename'] == base:
                s['latest_version'] = int(version)
                s['updated_at'] = _now_iso()
                break

    # Rebuild pool artifacts from all sources
    md_v, json_v = await _rebuild_pool(artifact_service, app_name, user_id, session_id, manifest)

    # Save updated manifest
    _ = await _save_manifest(artifact_service, app_name, user_id, session_id, manifest)

    return {
        'status': 'ok',
        'manifest': manifest,
        'pool': {'md_version': md_v, 'json_version': json_v},
    }
