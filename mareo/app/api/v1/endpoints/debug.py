from __future__ import annotations

import logging
from typing import Any

from fastapi import APIRouter, HTTPException, Request

router = APIRouter()
logger = logging.getLogger('google_adk.' + __name__)


@router.get('/list-apps')
def list_apps(request: Request) -> list[str]:
    agent_loader = request.app.state.agent_loader
    return agent_loader.list_agents()


@router.get('/debug/trace/{event_id}')
def get_trace_dict(event_id: str, request: Request) -> Any:
    trace_dict = request.app.state.trace_dict
    event_dict = trace_dict.get(event_id, None)
    if event_dict is None:
        raise HTTPException(status_code=404, detail='Trace not found')
    return event_dict


@router.get('/debug/trace/session/{session_id}')
def get_session_trace(session_id: str, request: Request) -> Any:
    memory_exporter = request.app.state.memory_exporter
    spans = memory_exporter.get_finished_spans(session_id)
    if not spans:
        return []
    return [
        {
            'name': s.name,
            'span_id': s.context.span_id,
            'trace_id': s.context.trace_id,
            'start_time': s.start_time,
            'end_time': s.end_time,
            'attributes': dict(s.attributes),
            'parent_span_id': s.parent.span_id if s.parent else None,
        }
        for s in spans
    ]
