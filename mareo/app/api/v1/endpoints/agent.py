from __future__ import annotations

import asyncio
import base64
import json
import logging
import os
import shutil
import traceback
from typing import Any, Literal

from fastapi import APIRouter, HTTPException, Query, Request, UploadFile, WebSocket
from fastapi.responses import StreamingResponse
from fastapi.websockets import WebSocketDisconnect
from google.adk.agents.live_request_queue import LiveRequest, LiveRequestQueue
from google.adk.agents.llm_agent import LlmAgent
from google.adk.agents.run_config import RunConfig, StreamingMode
from google.adk.cli.utils import cleanup, envs
from google.adk.runners import Runner
from google.genai import types
from google.genai.types import ThinkingConfig
from pydantic import ValidationError

from mareo.app.models.api_models import AgentRunRequest
from mareo.common.plugins import get_agent_plugins
from mareo.common.plugins.prompt_coherence_plugin import PromptCoherencePlugin
from mareo.common.plugins.remove_empty_tool_call_plugin import RemoveEmptyToolCallPlugin
from mareo.common.plugins.trace_session_plugin import TraceSessionPlugin

router = APIRouter()
logger = logging.getLogger('google_adk.' + __name__)


# Helper for custom JSON serialization (handles bytes/bytearray)
def _json_default(o: Any):
    """
    Fallback serializer for json.dumps that converts bytes/bytearray objects
    into UTF‑8 strings when possible, otherwise Base64‑encodes them.
    """
    if isinstance(o, (bytes, bytearray)):
        try:
            return json.loads(o.decode('utf‑8'))
        except UnicodeDecodeError:
            return base64.b64encode(o).decode('ascii')
    if isinstance(o, set):
        try:
            return list(o)
        except Exception as e:
            raise TypeError(f'Object of type {o.__class__.__name__} is not JSON serializable: {e}')
    raise TypeError(f'Object of type {o.__class__.__name__} is not JSON serializable')


async def get_runner_async(
    app_name: str,
    request: Request,
    streaming_thoughts: bool = False,
) -> Runner:
    """Returns the runner for the given app."""
    runner_dict = request.app.state.runner_dict
    _runners_to_clean = request.app.state._runners_to_clean
    if app_name in _runners_to_clean:
        _runners_to_clean.remove(app_name)
        runner = runner_dict.pop(app_name, None)
        await cleanup.close_runners(list([runner]))

    envs.load_dotenv_for_agent(os.path.basename(app_name), request.app.state.agents_dir)
    if app_name in runner_dict:
        return runner_dict[app_name]

    agent_loader = request.app.state.agent_loader
    root_agent = agent_loader.load_agent(app_name)
    if streaming_thoughts and issubclass(type(root_agent), LlmAgent):
        if root_agent.generate_content_config:
            if root_agent.generate_content_config.thinking_config:
                root_agent.generate_content_config.thinking_config.include_thoughts = True
            else:
                root_agent.generate_content_config.thinking_config = ThinkingConfig(include_thoughts=True)
        else:
            root_agent.generate_content_config = types.GenerateContentConfig(
                thinking_config=ThinkingConfig(include_thoughts=True)
            )
    # Load default plugins and extend with agent-specific registered plugins
    default_plugins = [
        TraceSessionPlugin(name='TraceSession'),
        RemoveEmptyToolCallPlugin(name='RemoveEmptyToolCall'),
        PromptCoherencePlugin(name='PromptCoherence'),
    ]
    agent_specific_plugins = get_agent_plugins(getattr(root_agent, 'name', app_name))

    runner = Runner(
        app_name=app_name,
        agent=root_agent,
        artifact_service=request.app.state.artifact_service,
        session_service=request.app.state.session_service,
        memory_service=request.app.state.memory_service,
        credential_service=request.app.state.credential_service,
        plugins=[*default_plugins, *agent_specific_plugins],
    )
    runner_dict[app_name] = runner
    return runner


@router.post('/builder/save', response_model_exclude_none=True)
async def builder_build(files: list[UploadFile], request: Request) -> bool:
    base_path = os.path.join(os.getcwd(), request.app.state.agents_dir)

    for file in files:
        try:
            if not file.filename:
                logger.exception('Agent name is missing in the input files')
                return False

            agent_name, filename = file.filename.split('/')

            agent_dir = os.path.join(base_path, agent_name)
            os.makedirs(agent_dir, exist_ok=True)
            file_path = os.path.join(agent_dir, filename)

            with open(file_path, 'wb') as buffer:
                shutil.copyfileobj(file.file, buffer)

        except Exception as e:
            logger.exception('Error in builder_build: %s', e)
            return False

    return True


@router.post('/run', response_model_exclude_none=True)
async def agent_run(
    req: AgentRunRequest,
    request: Request,
) -> list[Any]:
    session = await request.app.state.session_service.get_session(
        app_name=req.app_name, user_id=req.user_id, session_id=req.session_id
    )
    if not session:
        raise HTTPException(status_code=404, detail='Session not found')
    runner = await get_runner_async(
        req.app_name,
        request,
    )
    events = [
        event
        async for event in runner.run_async(
            user_id=req.user_id,
            session_id=req.session_id,
            new_message=req.new_message,
        )
    ]
    logger.info('Generated %s events in agent run', len(events))
    logger.debug('Events generated: %s', events)
    return events


@router.post('/run_sse')
async def agent_run_sse(
    req: AgentRunRequest,
    request: Request,
) -> StreamingResponse:
    session = await request.app.state.session_service.get_session(
        app_name=req.app_name, user_id=req.user_id, session_id=req.session_id
    )
    if not session:
        raise HTTPException(status_code=404, detail='Session not found')

    async def event_generator():
        try:
            stream_mode = StreamingMode.SSE if req.streaming else StreamingMode.NONE
            idx = 0
            runner = await get_runner_async(req.app_name, request, req.streaming_thoughts)
            async for event in runner.run_async(
                user_id=req.user_id,
                session_id=req.session_id,
                new_message=req.new_message,
                state_delta=req.state_delta,
                run_config=RunConfig(streaming_mode=stream_mode),
            ):
                # Convert to dict, add idx field, then serialize
                event_dict = event.model_dump(exclude_none=True, by_alias=True)
                event_dict['idx'] = idx
                sse_event = json.dumps(event_dict, default=_json_default)
                logger.debug('Generated event in agent run streaming: %s', sse_event)
                yield f'data: {sse_event}\n\n'
                idx += 1
        except Exception as e:
            logger.exception('Error in event_generator: %s', e)
            yield f'data: {{"error": "{str(e)}"}}\n\n'

    return StreamingResponse(
        event_generator(),
        media_type='text/event-stream',
    )


@router.websocket('/run_live')
async def agent_live_run(
    websocket: WebSocket,
    app_name: str,
    user_id: str,
    session_id: str,
    request: Request,
    modalities: list[Literal['TEXT', 'AUDIO']] = None,
) -> None:
    modalities = Query(default=['TEXT', 'AUDIO']) if modalities is None else modalities
    await websocket.accept()

    session = await request.app.state.session_service.get_session(
        app_name=app_name, user_id=user_id, session_id=session_id
    )
    if not session:
        await websocket.close(code=1002, reason='Session not found')
        return

    live_request_queue = LiveRequestQueue()

    async def forward_events():
        runner = await get_runner_async(
            app_name,
            request,
        )
        async for event in runner.run_live(session=session, live_request_queue=live_request_queue):
            await websocket.send_text(event.model_dump_json(exclude_none=True, by_alias=True))

    async def process_messages():
        try:
            while True:
                data = await websocket.receive_text()
                live_request_queue.send(LiveRequest.model_validate_json(data))
        except ValidationError as ve:
            logger.error('Validation error in process_messages: %s', ve)

    tasks = [
        asyncio.create_task(forward_events()),
        asyncio.create_task(process_messages()),
    ]
    done, pending = await asyncio.wait(tasks, return_when=asyncio.FIRST_EXCEPTION)
    try:
        for task in done:
            task.result()
    except WebSocketDisconnect:
        logger.info('Client disconnected during process_messages.')
    except Exception as e:
        logger.exception('Error during live websocket communication: %s', e)
        traceback.print_exc()
        WEBSOCKET_INTERNAL_ERROR_CODE = 1011
        WEBSOCKET_MAX_BYTES_FOR_REASON = 123
        await websocket.close(
            code=WEBSOCKET_INTERNAL_ERROR_CODE,
            reason=str(e)[:WEBSOCKET_MAX_BYTES_FOR_REASON],
        )
    finally:
        for task in pending:
            task.cancel()
