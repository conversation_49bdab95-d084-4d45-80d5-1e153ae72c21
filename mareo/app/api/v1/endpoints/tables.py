from __future__ import annotations

import copy
from typing import Any

from fastapi import APIRouter, HTTPException

from mareo.app.models.api_models import (
    BuildWideTableSchemaRequest,
    BuildWideTableSchemaResponse,
)

router = APIRouter(prefix='/api/v1')


def _validate_unique_nonempty(items: list[str], name: str):
    if not items:
        raise HTTPException(status_code=400, detail=f'{name} must be a non-empty list')
    if any(not isinstance(x, str) or not x for x in items):
        raise HTTPException(status_code=400, detail=f'All {name} must be non-empty strings')
    if len(set(items)) != len(items):
        raise HTTPException(status_code=400, detail=f'{name} must be unique')


def _build_wide_table_schema(req: BuildWideTableSchemaRequest) -> dict[str, Any]:
    _validate_unique_nonempty(req.rows, 'rows')
    _validate_unique_nonempty(req.columns, 'columns')

    col_properties: dict[str, Any] = {c: copy.deepcopy(req.cell_schema) for c in req.columns}

    row_objects: dict[str, Any] = {}
    for r in req.rows:
        row_schema: dict[str, Any] = {
            'type': 'object',
            'properties': copy.deepcopy(col_properties),
        }
        if req.required_columns:
            row_schema['required'] = list(req.columns)
        row_schema['additionalProperties'] = bool(req.additional_col_properties)
        row_objects[r] = row_schema

    schema: dict[str, Any] = {
        '$schema': 'https://json-schema.org/draft/2020-12/schema',
        'title': f'{req.table_name}（宽表）',
        'type': 'object',
        'properties': row_objects,
    }

    if req.required_rows:
        schema['required'] = list(req.rows)
    schema['additionalProperties'] = bool(req.additional_row_properties)

    return schema


def _build_pointer_examples(rows: list[str], columns: list[str]) -> list[str]:
    # Provide a couple of intuitive examples; fall back to Cartesian product limited to 5
    examples: list[str] = []
    # Zip first for readability similar to the example
    for r, c in zip(rows, columns):
        examples.append(f'/{r}/{c}')
        if len(examples) >= 5:
            return examples
    # If more capacity, add more combinations from the Cartesian product
    if len(examples) < 5:
        for r in rows:
            for c in columns:
                candidate = f'/{r}/{c}'
                if candidate not in examples:
                    examples.append(candidate)
                    if len(examples) >= 5:
                        return examples
    return examples


@router.post('/tables/schema/build', response_model=BuildWideTableSchemaResponse, response_model_exclude_none=True)
def build_table_schema(req: BuildWideTableSchemaRequest) -> BuildWideTableSchemaResponse:
    schema = _build_wide_table_schema(req)
    pointer_examples = _build_pointer_examples(req.rows, req.columns)
    return BuildWideTableSchemaResponse(json_schema=schema, pointer_examples=pointer_examples)
