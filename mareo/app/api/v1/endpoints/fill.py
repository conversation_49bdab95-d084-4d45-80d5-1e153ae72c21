from __future__ import annotations

import asyncio
import hashlib
import json
import logging
import uuid
from typing import Annotated, Any, Optional

import httpx
from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import StreamingResponse
from google.adk.agents import LlmAgent, ParallelAgent
from google.adk.runners import Runner
from google.genai import types
from pydantic import BaseModel, ConfigDict, Field, create_model

from mareo.app.models.api_models import (
    FillRequest,
    FillResponse,
    FillResponseMeta,
    ProvenanceEvidence,
    ProvenanceItem,
)
from mareo.common.llm.predefined.gemini_2_5_pro import get_gemini_2_5_pro
from mareo.common.models.report_schema import Claim
from mareo.common.plugins.prompt_coherence_plugin import PromptCoherencePlugin
from mareo.common.plugins.remove_empty_tool_call_plugin import RemoveEmptyToolCallPlugin
from mareo.common.plugins.trace_session_plugin import TraceSessionPlugin

router = APIRouter(prefix='/api/v1')
logger = logging.getLogger('google_adk.' + __name__)


PrimitiveTypes = {'string', 'number', 'integer', 'boolean'}


def _sha256_hex(data: Any) -> str:
    payload = json.dumps(data, ensure_ascii=False, sort_keys=True).encode('utf-8')
    return 'sha256:' + hashlib.sha256(payload).hexdigest()


def _escape_json_pointer(token: str) -> str:
    return token.replace('~', '~0').replace('/', '~1')


def _iter_leaf_groups(
    schema: dict[str, Any],
    base_pointer: str = '',
) -> list[tuple[str, dict[str, dict]]]:
    """Collect groups of leaf-object properties.

    A "leaf group" is an object whose properties are all primitives (or arrays of primitives),
    so that its child attributes should be generated together in one LLM request.
    Returns list of (group_pointer, properties_schema_dict).
    """

    results: list[tuple[str, dict[str, dict]]] = []

    def is_primitive_schema(s: dict) -> bool:
        t = s.get('type')
        if t in PrimitiveTypes:
            return True
        if t == 'array':
            items = s.get('items') or {}
            return items.get('type') in PrimitiveTypes
        return False

    def walk(obj_schema: dict, pointer: str) -> None:
        t = obj_schema.get('type')
        if t == 'object' and isinstance(obj_schema.get('properties'), dict):
            props: dict[str, Any] = obj_schema['properties']
            # If all properties are primitive-like, it's a leaf group
            if props and all(isinstance(v, dict) and is_primitive_schema(v) for v in props.values()):
                results.append((pointer or '/', props))
                return
            # Otherwise recurse into object-valued properties
            for k, v in props.items():
                if isinstance(v, dict):
                    new_ptr = (pointer + '/' + _escape_json_pointer(k)) if pointer else '/' + _escape_json_pointer(k)
                    walk(v, new_ptr)
        elif t == 'array' and isinstance(obj_schema.get('items'), dict):
            # Recurse into array items if they are objects
            items = obj_schema['items']
            if items.get('type') == 'object':
                walk(items, pointer + '/0' if pointer else '/0')

    walk(schema, base_pointer)
    return results


class _ForbidBaseModel(BaseModel):
    model_config = ConfigDict(extra='forbid')


def _py_type_for_schema(prop: dict):
    t = prop.get('type')
    if t == 'integer':
        return int
    if t == 'number':
        return float
    if t == 'boolean':
        return bool
    if t == 'array':
        item_t = (prop.get('items') or {}).get('type')
        if item_t == 'integer':
            return list[int]
        if item_t == 'number':
            return list[float]
        if item_t == 'boolean':
            return list[bool]
        return list[str]
    return str


def _build_value_claims_model(prop: dict) -> type[BaseModel]:
    value_type = _py_type_for_schema(prop)
    desc = prop.get('description')

    # Use an inner base to attach json_schema_extra for property ordering
    class _Ordered(_ForbidBaseModel):
        model_config = ConfigDict(
            extra='forbid',
            json_schema_extra={'propertyOrdering': ['claims', 'value']},
        )

    # Ensure field order in model definition matches the desired order
    ValueWithDesc = Annotated[Optional[value_type], Field(description=desc)]
    return create_model(  # type: ignore[no-any-return]
        'ValueClaims',
        __base__=_Ordered,
        claims=(list[Claim], Field(...)),
        value=(ValueWithDesc, Field(...)),
    )


def _build_group_response_schema(group_props: dict[str, dict]) -> type[BaseModel]:
    """Dynamically build a Pydantic model for a group's output.

    Each property is a nested { value, claims[] } model with strong typing.
    """
    fields: dict[str, tuple[type[BaseModel], Any]] = {}
    for name, prop in group_props.items():
        fields[name] = (_build_value_claims_model(prop), ...)
    return create_model('GroupResponse', __base__=_ForbidBaseModel, **fields)  # type: ignore[no-any-return]


def _set_nested(data: dict, pointer: str, obj: dict[str, Any]) -> None:
    """Set nested object at pointer within data, merging existing entries."""
    if pointer == '/' or pointer == '':
        if not isinstance(obj, dict):
            raise ValueError('root object must be dict')
        # merge at root
        for k, v in obj.items():
            data[k] = v
        return
    parts = [p for p in pointer.split('/') if p]
    cur = data
    for i, tok in enumerate(parts):
        key = tok.replace('~1', '/').replace('~0', '~')
        is_last = i == len(parts) - 1
        if is_last:
            existing = cur.get(key)
            if isinstance(existing, dict) and isinstance(obj, dict):
                existing.update(obj)
            else:
                cur[key] = obj
        else:
            if key not in cur or not isinstance(cur[key], dict):
                cur[key] = {}
            cur = cur[key]


def _coerce_level_to_int(level: Any) -> int:
    try:
        if isinstance(level, str):
            return int(level)
        if isinstance(level, (int, float)):
            return int(level)
    except Exception:  # noqa: BLE001
        pass
    return 2


def _extract_provenance_and_values(
    group_pointer: str,
    group_result: dict[str, Any],
) -> tuple[dict[str, Any], dict[str, list[ProvenanceItem]]]:
    values: dict[str, Any] = {}
    prov: dict[str, list[ProvenanceItem]] = {}
    for k, v in group_result.items():
        pointer = (
            (group_pointer.rstrip('/') + '/' + _escape_json_pointer(str(k)))
            if group_pointer and group_pointer != '/'
            else '/' + _escape_json_pointer(str(k))
        )
        value = v.get('value') if isinstance(v, dict) else v
        values[k] = value

        claims = v.get('claims') if isinstance(v, dict) else None
        if isinstance(claims, list):
            items: list[ProvenanceItem] = []
            for c in claims:
                try:
                    evidence = [
                        ProvenanceEvidence(source_id=e.get('source_id', ''), span_id=e.get('span_id', ''))
                        for e in (c.get('evidence') or [])
                        if isinstance(e, dict)
                    ]
                    derived_from = None
                    if isinstance(c.get('derived_from'), dict):
                        df = c['derived_from']
                        derived_from = {
                            'basis_pointers': [],  # mapping omitted for now
                            'reasoning': df.get('reasoning'),
                            'validation_needed': df.get('validation_needed') or [],
                        }
                    items.append(
                        ProvenanceItem(
                            level=_coerce_level_to_int(c.get('level', 2)),
                            kind=c.get('kind', 'paraphrase'),
                            text=str(c.get('text', value)),
                            confidence=float(c.get('confidence', 0.8)),
                            evidence=evidence,
                            derived_from=derived_from,
                        )
                    )
                except Exception:  # noqa: BLE001
                    continue
            if items:
                prov[pointer] = items
    return values, prov


async def _read_text_artifact(
    artifact_service, *, app_name: str, user_id: str, session_id: str, filename: str
) -> str | None:
    part = await artifact_service.load_artifact(
        app_name=app_name,
        user_id=user_id,
        session_id=session_id,
        filename=filename,
    )
    if not part:
        return None
    if part.text:
        return part.text
    if part.inline_data and part.inline_data.data:
        try:
            return part.inline_data.data.decode('utf-8')
        except Exception:  # noqa: BLE001
            return None
    return None


@router.post(
    '/apps/{app_name}/users/{user_id}/sessions/{session_id}/fill',
    response_model=FillResponse,
    response_model_exclude_none=True,
)
async def fill_endpoint_path(
    app_name: str,
    user_id: str,
    session_id: str,
    req: FillRequest,
    request: Request,
) -> FillResponse:
    if not isinstance(req.schema, dict):
        raise HTTPException(status_code=400, detail='schema must be a JSON object (JSON Schema)')

    # Ensure session exists
    session = await request.app.state.session_service.get_session(
        app_name=app_name, user_id=user_id, session_id=session_id
    )
    if not session:
        raise HTTPException(status_code=404, detail='Session not found')

    # Load manifest and pool
    artifact_service = request.app.state.artifact_service
    manifest_text = await _read_text_artifact(
        artifact_service,
        app_name=app_name,
        user_id=user_id,
        session_id=session_id,
        filename='evidence/manifest.json',
    )
    pool_text = await _read_text_artifact(
        artifact_service,
        app_name=app_name,
        user_id=user_id,
        session_id=session_id,
        filename='evidence/pool.md',
    )
    if not manifest_text or not pool_text:
        raise HTTPException(status_code=400, detail='EVIDENCE_POOL_NOT_FOUND')

    try:
        manifest = json.loads(manifest_text)
    except Exception as e:  # noqa: BLE001
        logger.exception('Invalid manifest JSON: %s', e)
        raise HTTPException(status_code=500, detail='MANIFEST_INVALID') from e

    available_ids = [s.get('short_id') for s in (manifest.get('sources') or []) if s.get('short_id')]
    available_ids_set = set(available_ids)

    # Determine allowed sources: empty/None means use all
    if not req.source_ids:
        used_ids = available_ids
    else:
        unknown = [sid for sid in req.source_ids if sid not in available_ids_set]
        if unknown:
            raise HTTPException(status_code=400, detail='UNKNOWN_SOURCE_IDS')
        used_ids = req.source_ids

    used_prefixes = tuple(f'{sid}::' for sid in used_ids)
    pool_lines = [line for line in pool_text.splitlines() if line.startswith(used_prefixes)]

    # Build leaf groups per requirement
    try:
        groups = _iter_leaf_groups(req.schema)
        if not groups:
            if req.schema.get('type') == 'object' and req.schema.get('properties'):
                groups = [('/', req.schema['properties'])]
            else:
                raise ValueError('No fillable leaf groups found in schema')
    except Exception as e:  # noqa: BLE001
        logger.exception('Schema parsing error: %s', e)
        raise HTTPException(status_code=400, detail='SCHEMA_INVALID') from e

    data: dict[str, Any] = {}
    provenance: dict[str, list[ProvenanceItem]] = {}

    def sanitize_state_key(ptr: str) -> str:
        if not ptr or ptr == '/':
            return 'root'
        return ptr.strip('/').replace('~', '_t_').replace('/', '__')

    sub_agents: list[LlmAgent] = []
    state_keys: list[tuple[str, str]] = []

    for idx, (group_pointer, group_props) in enumerate(groups):
        state_key = f'fill_group__{idx}__{sanitize_state_key(group_pointer)}'
        state_keys.append((group_pointer, state_key))
        response_schema_model = _build_group_response_schema(group_props)

        instruction = (
            '你是一个可溯源数据填报助手。\n'
            '只允许依据“证据池”片段撰写事实类主张（L1/L2/L3）。\n'
            '输出必须为严格 JSON：每个属性为 {"value": <值>, "claims": [...] }。不得输出额外文本。\n'
            '主张分层：L3 quote；L2 paraphrase；L1 derived；L0 insights/gaps（需 reasoning 与 validation_needed）。\n'
            '证据引用：只写 {"source_id":"Sx", "span_id":"..."}；避免长摘。\n'
            '按照给定属性 Schema 生成，按要求保持数值口径一致。若数据不存在，填写null。\n'
            f'你现在正在填写的是标题为{req.schema.get("title")},json pointer位于{group_pointer}的对象。'
        )
        if req.instructions:
            instruction += f'\n补充指示：{req.instructions}'

        model = get_gemini_2_5_pro(structured_outputs=True, response_format=response_schema_model)
        sub_agents.append(
            LlmAgent(
                name=f'fill_{idx}',
                model=model,
                instruction=instruction,
                output_key=state_key,
                description=f'Fill values for group at pointer {group_pointer or "/"}',
            )
        )

    parent = ParallelAgent(name='parallel_fill', sub_agents=sub_agents)

    # Use provided session for run
    runner = Runner(
        app_name=app_name,
        agent=parent,
        artifact_service=request.app.state.artifact_service,
        session_service=request.app.state.session_service,
        memory_service=request.app.state.memory_service,
        credential_service=request.app.state.credential_service,
        plugins=[
            TraceSessionPlugin(name='TraceSession'),
            RemoveEmptyToolCallPlugin(name='RemoveEmptyToolCall'),
            PromptCoherencePlugin(name='PromptCoherence'),
        ],
    )

    allowed_sources_str = '[' + ', '.join(used_ids) + ']'
    injected_text = (
        '这是所有的证据池片段：\n\n' + ('\n'.join(pool_lines)) + '\n\n' + f'仅可引用以下来源：{allowed_sources_str}\n'
    )
    if req.instructions:
        injected_text += f'补充指示：{req.instructions}\n'
    injected_text += '回答这个问题：请开始分组填充。'

    _ = [
        e
        async for e in runner.run_async(
            user_id=user_id,
            session_id=session_id,
            new_message=types.Content(role='user', parts=[types.Part.from_text(text=injected_text)]),
        )
    ]

    session_obj = await request.app.state.session_service.get_session(
        app_name=app_name, user_id=user_id, session_id=session_id
    )
    if isinstance(session_obj, dict):
        state = session_obj.get('state') or {}
    else:
        state = getattr(session_obj, 'state', None) or {}

    for group_pointer, state_key in state_keys:
        raw = state.get(state_key)
        if not raw:
            continue
        try:
            group_result = raw if isinstance(raw, dict) else json.decoder.JSONDecoder().raw_decode(raw)[0]
        except Exception:
            logger.warning('Invalid JSON in state for %s', state_key)
            continue
        if not isinstance(group_result, dict):
            continue
        values, group_prov = _extract_provenance_and_values(group_pointer, group_result)
        _set_nested(data, group_pointer, values)
        for p, items in group_prov.items():
            provenance.setdefault(p, []).extend(items)

    meta = FillResponseMeta(
        source_manifest=None,
        schema_hash=_sha256_hex(req.schema),
        unit_policy=(req.limits.numeric_unit if req.limits and req.limits.numeric_unit else None),
        model='openrouter/google/gemini-2.5-pro',
        task_id='t_' + uuid.uuid4().hex,
    )

    return FillResponse(data=data, provenance=provenance, meta=meta)


@router.post(
    '/apps/{app_name}/users/{user_id}/sessions/{session_id}/fill_stream',
)
async def fill_endpoint_path_stream(
    app_name: str,
    user_id: str,
    session_id: str,
    req: FillRequest,
    request: Request,
    keepalive_seconds: int = 10,
):
    """Stream keepalive whitespaces until result is ready, then emit JSON once.

    This keeps the single HTTP request open without SSE, avoids idle timeouts on proxies.
    The payload stays valid JSON by only streaming whitespace before the final JSON.
    """

    async def _gen():
        task = asyncio.create_task(
            fill_endpoint_path(app_name=app_name, user_id=user_id, session_id=session_id, req=req, request=request)
        )
        try:
            # Flush headers quickly and keep connection alive
            while not task.done():
                yield b' '  # JSON allows leading whitespace
                await asyncio.sleep(max(1, keepalive_seconds))
            result: FillResponse = await task
            yield result.model_dump_json().encode('utf-8')
        except asyncio.CancelledError:
            task.cancel()
            raise

    return StreamingResponse(_gen(), media_type='application/json')


@router.post('/fill', response_model=FillResponse, response_model_exclude_none=True)
async def fill_endpoint(req: FillRequest, request: Request) -> FillResponse:
    if not isinstance(req.schema, dict):
        raise HTTPException(status_code=400, detail='schema must be a JSON object (JSON Schema)')
    if not req.source_ids or any(not isinstance(s, str) or not s for s in req.source_ids):
        raise HTTPException(status_code=400, detail='source_ids must be a non-empty list of strings')

    # Build leaf groups per requirement (same-leaf-object attributes must be generated together)
    try:
        groups = _iter_leaf_groups(req.schema)
        if not groups:
            # As a fallback, treat top-level as a single group if it is primitive-only
            if req.schema.get('type') == 'object' and req.schema.get('properties'):
                groups = [('/', req.schema['properties'])]
            else:
                raise ValueError('No fillable leaf groups found in schema')
    except Exception as e:  # noqa: BLE001
        logger.exception('Schema parsing error: %s', e)
        raise HTTPException(status_code=400, detail='SCHEMA_INVALID') from e

    data: dict[str, Any] = {}
    provenance: dict[str, list[ProvenanceItem]] = {}

    # --- Build parallel LLM agents for each group ---
    def sanitize_state_key(ptr: str) -> str:
        if not ptr or ptr == '/':
            return 'root'
        return ptr.strip('/').replace('~', '_t_').replace('/', '__')

    sub_agents: list[LlmAgent] = []
    state_keys: list[tuple[str, str]] = []  # (group_pointer, state_key)

    for idx, (group_pointer, group_props) in enumerate(groups):
        state_key = f'fill_group__{idx}__{sanitize_state_key(group_pointer)}'
        state_keys.append((group_pointer, state_key))

        response_schema_model = _build_group_response_schema(group_props)

        # Instruction borrowed/adapted from report_agent, focused on per-group filling
        ','.join(req.source_ids)
        instruction = (
            '你是一个可溯源数据填报助手。\n'
            '只允许依据“证据池”片段撰写事实类主张（L1/L2/L3）。\n'
            '输出必须为严格 JSON：每个属性为 {"value": <值>, "claims": [...] }。不得输出额外文本。\n'
            '主张分层：L3 quote；L2 paraphrase；L1 derived；L0 insights/gaps（需 reasoning 与 validation_needed）。\n'
            '证据引用：只写 {"source_id":"Sx", "span_id":"..."}；避免长摘。\n'
            '按照给定属性 Schema 生成，按要求保持数值口径一致。若数据不存在，填写null。\n'
            f'你现在正在填写的是标题为{req.schema.get("title")},json pointer位于{group_pointer}的对象。'
        )
        if req.instructions:
            instruction += f'\n补充指示：{req.instructions}'

        model = get_gemini_2_5_pro(structured_outputs=True, response_format=response_schema_model)
        sub_agents.append(
            LlmAgent(
                name=f'fill_{idx}',
                model=model,
                instruction=instruction,
                # output_schema=response_schema_model,
                # generate_content_config=types.GenerateContentConfig(
                #     response_mime_type='application/json',
                #     max_output_tokens=2048,
                #     temperature=0.2,
                # ),
                output_key=state_key,
                description=f'Fill values for group at pointer {group_pointer or "/"}',
            )
        )

    parent = ParallelAgent(name='parallel_fill', sub_agents=sub_agents)

    # Prepare runner and ephemeral session
    app_name = 'fill_pipeline'
    user_id = 'user'
    session_id = 's_' + uuid.uuid4().hex

    session_service = request.app.state.session_service
    await session_service.create_session(app_name=app_name, user_id=user_id, state={}, session_id=session_id)

    runner = Runner(
        app_name=app_name,
        agent=parent,
        artifact_service=request.app.state.artifact_service,
        session_service=session_service,
        memory_service=request.app.state.memory_service,
        credential_service=request.app.state.credential_service,
        plugins=[
            TraceSessionPlugin(name='TraceSession'),
            RemoveEmptyToolCallPlugin(name='RemoveEmptyToolCall'),
            PromptCoherencePlugin(name='PromptCoherence'),
        ],
    )

    # Build injected new_message with demo evidence + allowed sources + instructions
    try:
        async with httpx.AsyncClient() as client:
            resp = await client.get('https://asset.mareo.ai/data.txt')
            resp.raise_for_status()
            demo_data = resp.content.decode('utf-8')
    except Exception:
        demo_data = ''

    ','.join(req.source_ids)
    injected_text = f'这是所有的证据池片段：\n\n{demo_data}\n\n'  # 仅可引用以下来源：[{allowed_sources}]\n'
    if req.instructions:
        injected_text += f'补充指示：{req.instructions}\n'
    injected_text += '回答这个问题：请开始分组填充。'

    _ = [
        e
        async for e in runner.run_async(
            user_id=user_id,
            session_id=session_id,
            new_message=types.Content(role='user', parts=[types.Part.from_text(text=injected_text)]),
        )
    ]

    # Collect results from session state
    session = await session_service.get_session(app_name=app_name, user_id=user_id, session_id=session_id)
    state = session.state or {}

    for group_pointer, state_key in state_keys:
        raw = state.get(state_key)
        if not raw:
            continue
        try:
            group_result = raw if isinstance(raw, dict) else json.decoder.JSONDecoder().raw_decode(raw)[0]
        except Exception:
            logger.warning('Invalid JSON in state for %s', state_key)
            continue
        if not isinstance(group_result, dict):
            continue

        values, group_prov = _extract_provenance_and_values(group_pointer, group_result)
        _set_nested(data, group_pointer, values)
        for p, items in group_prov.items():
            provenance.setdefault(p, []).extend(items)

    meta = FillResponseMeta(
        source_manifest=None,  # TODO: wire real manifest when retrieval is implemented
        schema_hash=_sha256_hex(req.schema),
        unit_policy=(req.limits.numeric_unit if req.limits and req.limits.numeric_unit else None),
        model='openrouter/google/gemini-2.5-pro',
        task_id='t_' + uuid.uuid4().hex,
    )

    return FillResponse(data=data, provenance=provenance, meta=meta)
