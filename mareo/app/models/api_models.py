from __future__ import annotations

from typing import Any, Optional

from google.adk.cli.cli_eval import EvalStatus
from google.adk.cli.utils import common
from google.adk.evaluation.eval_metrics import (
    EvalMetric,
    EvalMetricResult,
    EvalMetricResultPerInvocation,
)
from google.genai import types
from pydantic import Field

from mareo.common.models.report_schema import SourceManifestItem


class AgentRunRequest(common.BaseModel):
    app_name: str
    user_id: str
    session_id: str
    new_message: types.Content
    streaming: bool = False
    streaming_thoughts: bool = False
    state_delta: Optional[dict[str, Any]] = None


class AddSessionToEvalSetRequest(common.BaseModel):
    eval_id: str
    session_id: str
    user_id: str


class RunEvalRequest(common.BaseModel):
    eval_ids: list[str]  # if empty, then all evals in the eval set are run.
    eval_metrics: list[EvalMetric]


class RunEvalResult(common.BaseModel):
    eval_set_file: str
    eval_set_id: str
    eval_id: str
    final_eval_status: EvalStatus
    eval_metric_results: list[tuple[EvalMetric, EvalMetricResult]] = Field(
        deprecated=True,
        default=[],
        description=('This field is deprecated, use overall_eval_metric_results instead.'),
    )
    overall_eval_metric_results: list[EvalMetricResult]
    eval_metric_result_per_invocation: list[EvalMetricResultPerInvocation]
    user_id: str
    session_id: str


class GetEventGraphResult(common.BaseModel):
    dot_src: str


class BuildWideTableSchemaRequest(common.BaseModel):
    table_name: str
    rows: list[str]
    columns: list[str]
    # Defaults to number type per specification
    cell_schema: dict[str, Any] = Field(default_factory=lambda: {'type': 'number'})
    required_rows: bool = True
    required_columns: bool = True
    additional_row_properties: bool = False
    additional_col_properties: bool = False


class BuildWideTableSchemaResponse(common.BaseModel):
    json_schema: dict[str, Any]
    pointer_examples: list[str]


# =====================
# Fill API models
# =====================


class FillRetrieval(common.BaseModel):
    enable: bool = False
    topk: Optional[int] = None
    use_reports: Optional[list[str]] = None
    filters: Optional[dict[str, Any]] = None


class FillLimits(common.BaseModel):
    max_tokens: Optional[int] = None
    numeric_unit: Optional[str] = None
    rounding: Optional[int] = None


class FillMeta(common.BaseModel):
    task_label: Optional[str] = None
    return_debug: bool = False


class FillRequest(common.BaseModel):
    schema: dict[str, Any]
    source_ids: list[str]
    instructions: Optional[str] = None
    retrieval: Optional[FillRetrieval] = None
    limits: Optional[FillLimits] = None
    meta: Optional[FillMeta] = None


class ProvenanceEvidence(common.BaseModel):
    source_id: str
    span_id: str


class DerivedFromPointers(common.BaseModel):
    basis_pointers: list[str]
    reasoning: Optional[str] = None
    validation_needed: Optional[list[str]] = None


class ProvenanceItem(common.BaseModel):
    level: int
    kind: str
    text: str
    confidence: float
    evidence: list[ProvenanceEvidence] = Field(default_factory=list)
    derived_from: Optional[DerivedFromPointers] = None


class FillResponseMeta(common.BaseModel):
    source_manifest: Optional[list[SourceManifestItem]] = None
    schema_hash: Optional[str] = None
    unit_policy: Optional[str] = None
    model: Optional[str] = None
    task_id: Optional[str] = None


class FillResponse(common.BaseModel):
    data: dict[str, Any]
    provenance: dict[str, list[ProvenanceItem]]
    meta: Optional[FillResponseMeta] = None
