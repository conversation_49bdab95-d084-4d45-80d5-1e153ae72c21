from __future__ import annotations

import base64
import logging
import os

import litellm
from langfuse._client.get_client import get_client

from mareo.app.main import get_fast_api_app

logging.basicConfig(
    level=logging.INFO,
    format='{asctime} | {levelname:^8} | {process} | {threadName} | {name:^11} | {funcName} | {filename}:{lineno} | {message} ',
    style='{',
)

if os.getenv('DEBUG'):
    import logging

    logging.getLogger('google_adk').setLevel(logging.DEBUG)
    # logging.getLogger('mcp').setLevel(logging.DEBUG)
    # logging.getLogger('httpcore').setLevel(logging.DEBUG)
    # logging.getLogger('trace').setLevel(logging.DEBUG)
    # logging.getLogger('opentelemetry').setLevel(logging.DEBUG)

    litellm._turn_on_debug()

if os.environ.get('LANGFUSE_PUBLIC_KEY'):
    # from google.adk.cli.fast_api import get_fast_api_app
    LANGFUSE_AUTH = base64.b64encode(
        f'{os.environ.get("LANGFUSE_PUBLIC_KEY")}:{os.environ.get("LANGFUSE_SECRET_KEY")}'.encode()
    ).decode()
    # Configure OpenTelemetry endpoint & headers
    os.environ['OTEL_EXPORTER_OTLP_ENDPOINT'] = os.environ.get('LANGFUSE_HOST') + '/api/public/otel'
    os.environ['OTEL_EXPORTER_OTLP_HEADERS'] = f'Authorization=Basic {LANGFUSE_AUTH}'
    os.environ['LANGFUSE_TRACING_ENVIRONMENT'] = 'next'
    langfuse = get_client()
    # Verify connection
    if langfuse.auth_check():
        print('Langfuse client is authenticated and ready!')
    else:
        print('Authentication failed. Please check your credentials and host.')


app = get_fast_api_app(
    agents_dir='mareo/agent_hub',
    web=True,
    reload_agents=True,
    host='0.0.0.0',
    port=3000,
    allow_origins=['*'],
)
