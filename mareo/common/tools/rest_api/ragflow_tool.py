"""RAGFlow REST API Tool for accessing private knowledge base."""

import logging
import os
from typing import Any, Dict

import httpx
from google.adk.tools import ToolContext


async def query_ragflow_knowledge(query: str, tool_context: ToolContext) -> Dict[str, Any]:
    """Query RAGFlow private knowledge base to get information about specific topics.
    
    Args:
        query: The question to ask the RAGFlow knowledge base
        tool_context: ADK tool context for accessing state and configuration
        
    Returns:
        Dict containing the response from RAG<PERSON><PERSON>
    """
    # Get configuration from environment variables
    base_url = os.getenv('RAGFLOW_BASE_URL', 'https://ragflow.mareo.ai').rstrip('/')
    agent_id = os.getenv('RAGFLOW_AGENT_ID', '460ac064977911f0b7f200163e07c346')
    auth_token = os.getenv('RAGFLOW_AUTH_TOKEN', 'ragflow-M4NjMyYmIyOTc2YjExZjBhODAwMDAxNj')
    timeout = int(os.getenv('RAGFLOW_TIMEOUT', '30'))
    
    # Build the complete endpoint URL
    endpoint_url = f"{base_url}/api/v1/agents_openai/{agent_id}/chat/completions"
    
    try:
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {auth_token}"
        }
        
        payload = {
            "model": "model",
            "messages": [{"role": "user", "content": query}],
            "stream": False
        }
        
        logging.info(f"Querying RAGFlow with: {query}")
        
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(
                endpoint_url,
                headers=headers,
                json=payload
            )
            
            response.raise_for_status()
            result = response.json()
            
            # Extract the actual response content
            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0].get("message", {}).get("content", "")
                return {
                    "success": True,
                    "content": content,
                    "usage": result.get("usage", {}),
                    "model": result.get("model", "unknown")
                }
            else:
                return {
                    "success": False,
                    "error": "No valid response from RAGFlow",
                    "raw_response": result
                }
                
    except httpx.HTTPStatusError as e:
        error_msg = f"HTTP error {e.response.status_code}: {e.response.text}"
        logging.error(f"RAGFlow API error: {error_msg}")
        return {
            "success": False,
            "error": error_msg
        }
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        logging.error(f"RAGFlow tool error: {error_msg}")
        return {
            "success": False,
            "error": error_msg
        }