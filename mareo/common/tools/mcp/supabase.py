import logging
import os

from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters


def get_supabase_toolset() -> MCPToolset:
    """Create a Supabase MCP toolset instance using local stdio via npx.

    Environment variables:
    - SUPABASE_PROJECT_REF: Supabase project ref passed to the MCP server
    - SUPABASE_ACCESS_TOKEN: Personal access token for Supa<PERSON>, exported to the server env
    - SUPABASE_MCP_READ_ONLY: '1'/'true' to include --read-only (default enabled)
    """
    project_ref = os.getenv('SUPABASE_PROJECT_REF', '')
    access_token = os.getenv('SUPABASE_ACCESS_TOKEN', '')
    read_only_flag = os.getenv('SUPABASE_MCP_READ_ONLY', '1').lower() in {'1', 'true', 'yes'}

    args = ['-y', '@supabase/mcp-server-supabase@latest']
    if read_only_flag:
        args.append('--read-only')
    if project_ref:
        args.extend(['--project-ref', project_ref])

    env = {}
    if access_token:
        env['SUPABASE_ACCESS_TOKEN'] = access_token
    else:
        logging.warning('Env var SUPABASE_ACCESS_TOKEN is not set; MCP server may fail to authenticate')

    return MCPToolset(
        connection_params=StdioServerParameters(
            command='npx',
            args=args,
            env=env or None,
        )
    )
