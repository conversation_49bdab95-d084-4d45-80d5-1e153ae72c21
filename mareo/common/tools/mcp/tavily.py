import logging
import os

from fastapi.openapi.models import APIKey
from google.adk.auth import AuthCredential, AuthCredentialTypes
from google.adk.tools.mcp_tool import MCPToolset
from google.adk.tools.mcp_tool.mcp_session_manager import StreamableHTTPServerParams


def get_tavily_toolset() -> MCPToolset:
    """Create a new tavily toolset instance."""
    if not (tavily_key := os.getenv('TAVILY_KEY', '')):
        logging.warning('Env var TAVILY_KEY is not set')
    return MCPToolset(
        connection_params=StreamableHTTPServerParams(url=os.getenv('TAVILY_BASE', '')),
        auth_scheme=APIKey(name='s', **{'in': 'header'}),
        auth_credential=AuthCredential(auth_type=AuthCredentialTypes.API_KEY, api_key=tavily_key),
    )
