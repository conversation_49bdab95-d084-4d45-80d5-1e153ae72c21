import logging
from typing import Optional

from google.adk.agents.callback_context import CallbackContext
from google.adk.models.llm_response import LlmResponse
from google.adk.plugins.base_plugin import BasePlugin
from google.genai import types

logger = logging.getLogger(__name__)


class RemoveEmptyToolCallPlugin(BasePlugin):
    async def after_model_callback(
        self, *, callback_context: CallbackContext, llm_response: LlmResponse
    ) -> Optional[LlmResponse]:
        """
        这是一个 after_model_callback 函数，用于检查并移除
        那些 `name` 和 `args` 都为空的函数调用。

        Args:
            callback_context: 回调上下文，提供当前代理运行环境的信息。
            llm_response: 从 LLM 返回的原始响应对象。

        Returns:
            如果移除了任何空的函数调用，则返回一个新的、修改过的 LlmResponse 对象。
            如果没有进行任何修改，则返回 None。
        """
        # 检查响应中是否有内容需要处理
        if not llm_response.content or not llm_response.content.parts:
            logger.log(logging.DEBUG, 'no content in this response')
            return None  # 无需修改，继续使用原始响应

        modified = False
        filtered_parts = []

        for part in llm_response.content.parts:
            # 检查这个 part 是否是一个函数调用
            if part.function_call:
                # 检查 name 和 args 是否都为空
                is_name_empty = not part.function_call.name
                is_args_empty = not part.function_call.args

                if is_name_empty and is_args_empty:
                    # 这是一个空的函数调用，我们跳过它
                    logger.log(logging.DEBUG, 'removing empty tool call')
                    modified = True
                    continue  # 不将这个 part 添加到过滤后的列表中

            # 如果不是空的函数调用，或者根本不是函数调用，则保留这个 part
            filtered_parts.append(part)

        # 如果我们移除了任何 part，就需要返回一个新的 LlmResponse
        if modified:
            # 如果所有的 part 都被移除了，响应内容就变为空
            if not filtered_parts:
                new_content = None
            else:
                # 使用过滤后的 parts 创建新的内容
                new_content = types.Content(role=llm_response.content.role, parts=filtered_parts)

            # 创建一个新的 LlmResponse，并从原始响应中复制其他相关属性
            new_response = LlmResponse(
                content=new_content,
                grounding_metadata=llm_response.grounding_metadata,
                # 如果需要，也可以复制其他字段
            )
            logger.log(logging.DEBUG, 'returning modified llm response')
            return new_response

        # 如果没有进行任何修改，返回 None 以使用原始响应
        logger.log(logging.DEBUG, 'no empty tool call found, returning original response')
        return None
