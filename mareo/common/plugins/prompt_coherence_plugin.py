from typing import Optional

from google.adk.agents.callback_context import CallbackContext
from google.adk.models import LlmRequest, LlmResponse
from google.adk.plugins import BasePlugin


class PromptCoherencePlugin(BasePlugin):
    """
    This plugin ensures that the prompt is coherent and consistent.
    """

    async def before_model_callback(
        self, *, callback_context: CallbackContext, llm_request: LlmRequest
    ) -> Optional[LlmResponse]:
        if 'system_prompt' not in callback_context.state:
            callback_context.state['system_prompt'] = str(llm_request.config.system_instruction)
        return None
