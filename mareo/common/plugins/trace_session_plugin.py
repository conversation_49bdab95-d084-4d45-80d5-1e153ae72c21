import logging
from typing import Optional

from google.adk.agents import InvocationContext
from google.adk.plugins.base_plugin import BasePlugin
from google.genai import types
from opentelemetry import trace

logger = logging.getLogger(__name__)


class TraceSessionPlugin(BasePlugin):
    async def before_run_callback(self, *, invocation_context: InvocationContext) -> Optional[types.Content]:
        try:
            session = invocation_context.session
        except Exception:
            session = None
        if not session:
            logger.warning('Trace plugin: Session object not found in context.')
            return None

        user_id = session.user_id
        session_id = session.id

        trace.get_current_span().set_attribute('user.id', user_id)
        trace.get_current_span().set_attribute('session.id', session_id)
        return None
