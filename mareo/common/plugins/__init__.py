from __future__ import annotations

from collections import defaultdict
from typing import Callable, Iterable, TypeAlias

try:
    # Optional import for type checking; avoid hard dependency at import time
    from google.adk.plugins.base_plugin import BasePlugin  # type: ignore
except Exception:  # pragma: no cover - best-effort typing when ADK not present

    class BasePlugin:  # type: ignore
        ...


_PluginFactory: TypeAlias = Callable[[], BasePlugin]


# Internal registry mapping agent name -> list of plugin factories (or instances)
_agent_to_plugin_specs: dict[str, list[BasePlugin | _PluginFactory]] = defaultdict(list)


def register_agent_plugins(
    agent_name: str,
    plugins: Iterable[BasePlugin | _PluginFactory],
    *,
    replace: bool = False,
) -> None:
    """Register plugins for a given agent without modifying Agent class.

    The registration can be called from the agent module at definition time, e.g.:

    ```python
    from mareo.common.plugins import register_agent_plugins
    from my_pkg.plugins import MyPlugin

    register_agent_plugins('my_agent', [lambda: MyPlugin(name='MyPlugin')])
    ```

    - If `replace=True`, the provided list replaces previously registered ones.
    - Otherwise, it appends to the existing list for that agent.
    """

    specs = list(plugins)
    if replace:
        _agent_to_plugin_specs[agent_name] = specs
    else:
        _agent_to_plugin_specs[agent_name].extend(specs)


def get_agent_plugins(agent_name: str) -> list[BasePlugin]:
    """Instantiate and return all plugins registered for the given agent.

    Supports both plugin instances and zero-arg factories.
    Returns an empty list if none are registered.
    """

    instantiated: list[BasePlugin] = []
    for spec in _agent_to_plugin_specs.get(agent_name, []):
        if callable(spec):  # factory
            plugin = spec()  # type: ignore[call-arg]
            instantiated.append(plugin)
        else:
            instantiated.append(spec)
    return instantiated
