import copy
from typing import Optional

import httpx
from google.adk.agents import Invocation<PERSON>ontext
from google.adk.plugins import BasePlugin
from google.genai import types


class InjectAttachmentPlugin(BasePlugin):
    async def on_user_message_callback(
        self,
        *,
        invocation_context: InvocationContext,
        user_message: types.Content,
    ) -> Optional[types.Content]:
        if not invocation_context.session.events:
            async with httpx.AsyncClient() as client:
                resp = await client.get('https://asset.mareo.ai/data.txt')
                resp.raise_for_status()
                data = resp.content.decode('utf-8')
            injected_message = copy.deepcopy(user_message)
            injected_message.parts.insert(
                0, types.Part.from_text(text=f'这是所有的证据池片段：\n\n{data}\n\n回答这个问题：')
            )
            return injected_message
        return None
