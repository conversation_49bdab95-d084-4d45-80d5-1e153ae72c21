"""RAGFlow API client for knowledge-based chat completions."""

import json
import logging
import os
from typing import Any, AsyncGenerator, Dict, List, Optional

import httpx
from google.adk.models.base_llm import BaseLlm
from google.adk.models.llm_request import LlmRequest
from google.adk.models.llm_response import LlmResponse
from google.genai import types
from pydantic import Field

logger = logging.getLogger('google_adk.' + __name__)


class RAGFlowClient(BaseLlm):
    """RAGFlow knowledge agent client for accessing private knowledge bases."""
    
    agent_id: str = Field(default="460ac064977911f0b7f200163e07c346")
    base_url: str = Field(default="https://ragflow.mareo.ai")
    auth_token: Optional[str] = Field(default=None)
    endpoint: str = Field(default="", init=False)
    
    def __init__(
        self,
        agent_id: str = "460ac064977911f0b7f200163e07c346",
        base_url: str = "https://ragflow.mareo.ai",
        auth_token: Optional[str] = None,
        model: str = "ragflow-knowledge-agent",
        **kwargs
    ):
        """Initialize RAGFlow client.
        
        Args:
            agent_id: RAGFlow agent ID
            base_url: RAGFlow API base URL
            auth_token: Authentication token (defaults to env var)
            model: Model identifier for compatibility
            **kwargs: Additional arguments
        """
        # Set auth token with fallback to environment variable
        if auth_token is None:
            auth_token = os.getenv(
                'RAGFLOW_AUTH_TOKEN', 
                'ragflow-M4NjMyYmIyOTc2YjExZjBhODAwMDAxNj'
            )
        
        super().__init__(
            model=model,
            agent_id=agent_id,
            base_url=base_url.rstrip('/'),
            auth_token=auth_token,
            **kwargs
        )
        
        # Set computed endpoint after initialization
        self.endpoint = f"{self.base_url}/api/v1/agents_openai/{self.agent_id}/chat/completions"
        
    def _convert_request_to_messages(self, llm_request: LlmRequest) -> List[Dict[str, Any]]:
        """Convert LlmRequest to RAGFlow messages format."""
        messages = []
        
        # Add system instruction if present
        if llm_request.config and llm_request.config.system_instruction:
            messages.append({
                "role": "system",
                "content": llm_request.config.system_instruction
            })
        
        # Convert content messages
        for content in llm_request.contents or []:
            role = "assistant" if content.role in ["model", "assistant"] else "user"
            
            # Extract text content from parts
            text_parts = []
            for part in content.parts:
                if part.text:
                    text_parts.append(part.text)
                elif part.function_call:
                    # Handle function calls if needed
                    func_text = f"Function call: {part.function_call.name}({part.function_call.args})"
                    text_parts.append(func_text)
                elif part.function_response:
                    # Handle function responses if needed
                    resp_text = f"Function response: {part.function_response.response}"
                    text_parts.append(resp_text)
            
            if text_parts:
                messages.append({
                    "role": role,
                    "content": " ".join(text_parts)
                })
        
        return messages
    
    async def _make_request(self, messages: List[Dict[str, Any]], stream: bool = False) -> Dict[str, Any]:
        """Make HTTP request to RAGFlow API."""
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.auth_token}"
        }
        
        payload = {
            "model": "model",  # RAGFlow expects this field
            "messages": messages,
            "stream": stream
        }
        
        async with httpx.AsyncClient(timeout=60.0) as client:
            try:
                response = await client.post(
                    self.endpoint,
                    headers=headers,
                    json=payload
                )
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"RAGFlow API request failed: {e}")
                raise ValueError(f"RAGFlow API error: {e}")
    
    def _convert_response_to_llm_response(self, ragflow_response: Dict[str, Any]) -> LlmResponse:
        """Convert RAGFlow response to LlmResponse."""
        try:
            # Extract content from RAGFlow response
            choices = ragflow_response.get("choices", [])
            if not choices:
                raise ValueError("No choices in RAGFlow response")
            
            message = choices[0].get("message", {})
            content_text = message.get("content", "")
            
            # Create response parts
            parts = []
            if content_text:
                parts.append(types.Part.from_text(text=content_text))
            
            # Create LlmResponse
            llm_response = LlmResponse(
                content=types.Content(role='model', parts=parts),
                partial=False
            )
            
            # Add usage metadata if available
            usage = ragflow_response.get("usage", {})
            if usage:
                llm_response.usage_metadata = types.GenerateContentResponseUsageMetadata(
                    prompt_token_count=usage.get("prompt_tokens", 0),
                    candidates_token_count=usage.get("completion_tokens", 0),
                    total_token_count=usage.get("total_tokens", 0)
                )
            
            return llm_response
            
        except Exception as e:
            logger.error(f"Failed to convert RAGFlow response: {e}")
            raise ValueError(f"Response conversion error: {e}")
    
    async def generate_content_async(self, llm_request: LlmRequest, stream: bool = False) -> AsyncGenerator[LlmResponse, None]:
        """Generate content using RAGFlow API."""
        self._maybe_append_user_content(llm_request)
        
        # Log request for debugging
        logger.debug(f"RAGFlow request: {llm_request}")
        
        # Convert to RAGFlow format
        messages = self._convert_request_to_messages(llm_request)
        
        if not messages:
            raise ValueError("No messages to send to RAGFlow")
        
        # Make API request
        ragflow_response = await self._make_request(messages, stream=stream)
        
        # Convert and yield response
        llm_response = self._convert_response_to_llm_response(ragflow_response)
        yield llm_response
    
    @staticmethod
    def supported_models() -> List[str]:
        """Return supported models."""
        return ["ragflow-knowledge-agent"]


def get_ragflow_client(**kwargs) -> RAGFlowClient:
    """Factory function to create RAGFlow client with tool choice required."""
    return RAGFlowClient(**kwargs)


def get_ragflow_model_with_tool_choice(tool_choice: str = 'required', **kwargs) -> RAGFlowClient:
    """
    Factory function that mimics get_qwen3_235b signature for easy replacement.
    
    Args:
        tool_choice: Tool choice parameter (maintained for compatibility)
        **kwargs: Additional arguments passed to RAGFlow client
        
    Returns:
        RAGFlowClient instance configured for knowledge-based responses
    """
    # RAGFlow inherently uses tools (knowledge base), so tool_choice is always effective
    return RAGFlowClient(**kwargs)


# Alias for easier migration from Qwen to RAGFlow
get_ragflow_235b = get_ragflow_model_with_tool_choice