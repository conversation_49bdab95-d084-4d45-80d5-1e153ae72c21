from functools import partial
from typing import Callable, Final

from mareo.common.llm.lite_llm_reasoning import LiteLlmReasoning

# Expose a prefilled constructor for reuse
get_gemini_2_5_flash: Final[Callable[..., LiteLlmReasoning]] = partial(
    LiteLlmReasoning,
    model='openrouter/google/gemini-2.5-flash',
    max_completion_tokens=65536,
    require_parameters=True,
)


def _build_gemini() -> LiteLlmReasoning:
    return get_gemini_2_5_flash()


def __getattr__(name: str):
    match name:
        case 'gemini_2_5_flash':
            return _build_gemini()

    raise AttributeError(f'module {__name__!r} has no attribute {name!r}')


gemini_2_5_flash: Final[LiteLlmReasoning]  # type: ignore[assignment]
