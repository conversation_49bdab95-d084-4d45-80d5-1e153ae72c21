import os
from functools import partial
from typing import Callable, Final

from mareo.common.llm.lite_llm_reasoning import LiteLlmReasoning

# Expose prefilled constructors for reuse
get_qwen3_235b: Final[Callable[..., LiteLlmReasoning]] = partial(
    LiteLlmReasoning,
    model='openrouter/qwen/qwen3-235b-a22b-2507',
    max_completion_tokens=65536,
    provider={'ignore': ['baseten/fp8', 'deepinfra/fp8'], 'order': ['cerebras']},
    require_parameters=True,
)


def _build_qwen3_235b() -> LiteLlmReasoning:
    """Create and return a qwen3_235b model instance."""
    return get_qwen3_235b()


get_qwen3_235b_official: Final[Callable[..., LiteLlmReasoning]] = partial(
    LiteLlmReasoning,
    model='openai/qwen3-235b-a22b-instruct-2507',
    api_base='https://dashscope.aliyuncs.com/compatible-mode/v1',
    api_key=os.getenv('OPENAI_API_KEY', None),
    max_completion_tokens=65536,
    require_parameters=True,
)


def _build_qwen3_235b_official() -> LiteLlmReasoning:
    """Create and return a qwen3_235b model instance."""
    return get_qwen3_235b_official()


get_qwen3_235b_siliconflow: Final[Callable[..., LiteLlmReasoning]] = partial(
    LiteLlmReasoning,
    model='openai/Qwen/Qwen3-235B-A22B-Instruct-2507',
    api_base='https://api.siliconflow.cn/v1',
    api_key=os.getenv('OPENAI_API_KEY', None),
    max_completion_tokens=65536,
    require_parameters=True,
)


def _build_qwen3_235b_siliconflow() -> LiteLlmReasoning:
    """Create and return a qwen3_235b model instance."""
    return get_qwen3_235b_siliconflow()


def __getattr__(name: str):
    match name:
        case 'qwen3_235b':
            return _build_qwen3_235b()
        case 'qwen3_235b_official':
            return _build_qwen3_235b_official()
        case 'qwen3_235b_siliconflow':
            return _build_qwen3_235b_siliconflow()

    raise AttributeError(f'module {__name__!r} has no attribute {name!r}')


qwen3_235b: Final[LiteLlmReasoning]  # type: ignore[assignment]
qwen3_235b_official: Final[LiteLlmReasoning]  # type: ignore[assignment]
qwen3_235b_siliconflow: Final[LiteLlmReasoning]  # type: ignore[assignment]
get_qwen3_235b: Final[Callable[..., LiteLlmReasoning]]  # type: ignore[assignment]
get_qwen3_235b_official: Final[Callable[..., LiteLlmReasoning]]  # type: ignore[assignment]
get_qwen3_235b_siliconflow: Final[Callable[..., LiteLlmReasoning]]  # type: ignore[assignment]
