from functools import partial
from typing import Callable, Final

from mareo.common.llm.lite_llm_reasoning import LiteLlmReasoning

# Expose a prefilled constructor for reuse
get_gemini_2_5_pro: Final[Callable[..., LiteLlmReasoning]] = partial(
    LiteLlmReasoning,
    model='openrouter/google/gemini-2.5-pro',
    max_completion_tokens=65536,
    require_parameters=True,
)


def _build_gemini() -> LiteLlmReasoning:
    return get_gemini_2_5_pro()


def __getattr__(name: str):
    match name:
        case 'gemini_2_5_pro':
            return _build_gemini()

    raise AttributeError(f'module {__name__!r} has no attribute {name!r}')


gemini_2_5_pro: Final[LiteLlmReasoning]  # type: ignore[assignment]
