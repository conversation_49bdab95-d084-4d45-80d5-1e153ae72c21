from functools import partial
from typing import Callable, Final

from mareo.common.llm.lite_llm_reasoning import LiteLlmReasoning

get_r1_0528: Final[Callable[..., LiteLlmReasoning]] = partial(
    LiteLlmReasoning,
    model='openrouter/deepseek/deepseek-r1-0528',
    max_completion_tokens=20000,
    **{'provider': {'ignore': ['baseten/fp8']}},
    stop=['<｜tool▁call▁end｜>'],
)


def _build_r1_0528() -> LiteLlmReasoning:
    """Create and return a r1_0528 model instance."""
    return get_r1_0528()


def __getattr__(name: str):
    if name == 'r1_0528':
        return _build_r1_0528()
    raise AttributeError(f'module {__name__!r} has no attribute {name!r}')


r1_0528: Final[LiteLlmReasoning]  # type: ignore[assignment]
get_r1_0528: Final[Callable[..., LiteLlmReasoning]]  # type: ignore[assignment]
