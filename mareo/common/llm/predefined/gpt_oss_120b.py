from functools import partial
from typing import Callable, Final

from mareo.common.llm.lite_llm_reasoning import LiteLlmReasoning

get_gpt_oss_120b: Final[Callable[..., LiteLlmReasoning]] = partial(
    LiteLlmReasoning,
    model='openrouter/openai/gpt-oss-120b',
    max_completion_tokens=65536,
    provider={'ignore': ['baseten/fp8', 'deepinfra/fp8'], 'order': ['cerebras']},
    require_parameters=True,
)


def _build_gpt_oss_120b() -> LiteLlmReasoning:
    """Create and return a qwen3_235b model instance."""
    return get_gpt_oss_120b()


def __getattr__(name: str):
    if name == 'gpt_oss_120b':
        return _build_gpt_oss_120b()
    raise AttributeError(f'module {__name__!r} has no attribute {name!r}')


gpt_oss_120b: Final[LiteLlmReasoning]  # type: ignore[assignment]
get_gpt_oss_120b: Final[Callable[..., LiteLlmReasoning]]  # type: ignore[assignment]
