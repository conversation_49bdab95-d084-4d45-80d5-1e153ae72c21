from functools import partial
from typing import Callable, Final

from mareo.common.llm.lite_llm_reasoning import LiteLlmReasoning

get_claude_sonnet4: Final[Callable[..., LiteLlmReasoning]] = partial(
    LiteLlmReasoning,
    model='openrouter/anthropic/claude-sonnet-4',
    max_completion_tokens=20000,
    require_parameters=True,
)


def _build_claude_sonnet4() -> LiteLlmReasoning:
    """Create and return a qwen3_235b model instance."""
    return get_claude_sonnet4()


def __getattr__(name: str):
    if name == 'claude_sonnet4':
        return _build_claude_sonnet4()
    raise AttributeError(f'module {__name__!r} has no attribute {name!r}')


claude_sonnet4: Final[LiteLlmReasoning]  # type: ignore[assignment]
get_claude_sonnet4: Final[Callable[..., LiteLlmReasoning]]  # type: ignore[assignment]
