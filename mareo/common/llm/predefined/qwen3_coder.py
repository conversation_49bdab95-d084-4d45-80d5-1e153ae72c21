from functools import partial
from typing import Callable, Final

from mareo.common.llm.lite_llm_reasoning import LiteLlmReasoning

get_qwen3_coder: Final[Callable[..., LiteLlmReasoning]] = partial(
    LiteLlmReasoning,
    model='openrouter/qwen/qwen3-coder',
    max_completion_tokens=20000,
    provider={'ignore': ['baseten/fp8', 'chutes']},
    require_parameters=True,
    tempreture=0,
)


def _build_qwen3_coder() -> LiteLlmReasoning:
    """Create and return a qwen3_235b model instance."""
    return get_qwen3_coder()


def __getattr__(name: str):
    if name == 'qwen3_coder':
        return _build_qwen3_coder()
    raise AttributeError(f'module {__name__!r} has no attribute {name!r}')


qwen3_coder: Final[LiteLlmReasoning]  # type: ignore[assignment]
get_qwen3_coder: Final[Callable[..., LiteLlmReasoning]]  # type: ignore[assignment]
