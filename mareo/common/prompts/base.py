import logging
import os
import time
from abc import ABC, abstractmethod
from asyncio import Lock
from typing import (
    Any,
    Awaitable,
    Callable,
    Generic,
    NamedTuple,
    Optional,
    TypeVar,
    Union,
)

from google.adk.agents.readonly_context import ReadonlyContext

T = TypeVar('T')
logger = logging.getLogger(__name__)


class CacheEntry(NamedTuple, Generic[T]):
    """Cache entry with TTL support."""

    value: T
    timestamp: float


class PromptProvider(ABC, Generic[T]):
    """Base class for prompt providers with caching and TTL support."""

    def __init__(self, cache_ttl: Optional[int] = None, fallback_prompts: Optional[dict[str, str]] = None):
        """Initialize the prompt provider.

        Args:
            cache_ttl: Cache TTL in seconds. If None, uses CACHE_TTL environment variable or default.
            fallback_prompts: Dictionary of fallback prompts keyed by prompt name/identifier.
        """
        self._cache: dict[tuple[str, ...], <PERSON><PERSON><PERSON><PERSON>ry[T]] = {}
        self._cache_lock = Lock()
        self._cache_ttl = cache_ttl or int(os.getenv('CACHE_TTL', '60'))
        self._fallback_prompts = fallback_prompts or {}
        self._load_fallback_prompts_from_env()

    def _load_fallback_prompts_from_env(self) -> None:
        """Load fallback prompts from environment variables.

        Environment variables should follow the pattern: FALLBACK_PROMPT_<PROMPT_NAME>=<prompt_content>
        Example: FALLBACK_PROMPT_NEXT_CHAT="You are a helpful AI assistant..."
        """
        for key, value in os.environ.items():
            if key.startswith('FALLBACK_PROMPT_'):
                prompt_name = key[len('FALLBACK_PROMPT_') :].lower().replace('_', '-')
                self._fallback_prompts[prompt_name] = value
                logger.debug(f"Loaded fallback prompt for '{prompt_name}' from environment")

    def _get_fallback_prompt(self, prompt_identifier: str) -> Optional[str]:
        """Get fallback prompt for the given identifier.

        Args:
            prompt_identifier: The prompt name/identifier to look up.

        Returns:
            The fallback prompt string if available, None otherwise.
        """
        # Try exact match first
        if prompt_identifier in self._fallback_prompts:
            return self._fallback_prompts[prompt_identifier]

        # Try case-insensitive match with normalization
        normalized_identifier = prompt_identifier.lower().replace('_', '-')
        for key, value in self._fallback_prompts.items():
            if key.lower().replace('_', '-') == normalized_identifier:
                return value

        return None

    def add_fallback_prompt(self, key: str, prompt_content: str) -> None:
        """Add or update a fallback prompt.

        Args:
            key: The key/identifier for the fallback prompt.
            prompt_content: The fallback prompt content.
        """
        self._fallback_prompts[key] = prompt_content
        logger.debug(f"Added fallback prompt for '{key}'")

    def _is_cache_expired(self, cache_entry: CacheEntry[T]) -> bool:
        """Check if cache entry has expired based on TTL."""
        return time.time() - cache_entry.timestamp > self._cache_ttl

    def _cleanup_expired_cache(self) -> None:
        """Remove expired entries from cache. Should be called with lock held."""
        current_time = time.time()
        expired_keys = [key for key, entry in self._cache.items() if current_time - entry.timestamp > self._cache_ttl]
        for key in expired_keys:
            self._cache.pop(key, None)

    def _get_cache_key(self, *args: Any) -> tuple[str, ...]:
        """Generate cache key from arguments."""
        return tuple(str(arg) for arg in args)

    async def _get_prompt(self, context: Optional[ReadonlyContext] = None, *args: Any) -> Union[str, T]:
        """Get prompt with caching support and fallback mechanism. First checks context.state for system_prompt."""
        # Check for system_prompt in context.state first
        # make sure PromptCoherencePlugin from mareo.common.plugins.prompt_coherence_plugin is enabled
        if context and (sys_prompt := context.state.get('system_prompt', None)):
            return sys_prompt

        cache_key = self._get_cache_key(*args)
        # get-or-set
        async with self._cache_lock:
            # Try to get from cache first
            if cache_key in self._cache:
                if not self._is_cache_expired(cache_entry := self._cache[cache_key]):
                    return cache_entry.value
                else:
                    # Remove only this expired entry
                    self._cache.pop(cache_key, None)

            # Try to fetch from provider with fallback support
            try:
                value = await self._fetch_prompt(*args)
                # Store in cache
                self._cache[cache_key] = CacheEntry(value=value, timestamp=time.time())
                return value
            except Exception as e:
                logger.warning(f'Failed to fetch prompt from provider: {e}')
                # Try to get fallback prompt
                fallback_prompt = self._get_fallback_for_args(*args)
                if fallback_prompt:
                    logger.info(f'Using fallback prompt for args: {args}')
                    return fallback_prompt
                else:
                    logger.error(f'No fallback prompt available for args: {args}')
                    raise

    async def clear_cache(self) -> None:
        """Clear all cached entries."""
        async with self._cache_lock:
            self._cache.clear()

    async def cleanup_expired_cache(self) -> int:
        """Manually clean up expired cache entries.

        Returns:
            Number of entries that were cleaned up.
        """
        async with self._cache_lock:
            entries_before = len(self._cache)
            self._cleanup_expired_cache()
            entries_after = len(self._cache)
            return entries_before - entries_after

    async def get_cache_stats(self, cleanup_expired: bool = True) -> dict[str, Any]:
        """Get cache statistics.

        Args:
            cleanup_expired: Whether to clean up expired entries while calculating stats.
        """
        async with self._cache_lock:
            total_entries_before = len(self._cache)
            expired_entries = sum(1 for entry in self._cache.values() if self._is_cache_expired(entry))

            if cleanup_expired:
                # Clean up expired entries during stats collection
                self._cleanup_expired_cache()
                total_entries_after = len(self._cache)
                cleaned_entries = total_entries_before - total_entries_after
            else:
                total_entries_after = total_entries_before
                cleaned_entries = 0

            return {
                'total_entries': total_entries_after,
                'valid_entries': total_entries_after,
                'expired_entries_found': expired_entries,
                'cleaned_entries': cleaned_entries,
                'cache_ttl': self._cache_ttl,
            }

    @abstractmethod
    async def _fetch_prompt(self, *args: Any) -> T:
        """Fetch prompt from the underlying provider. Must be implemented by subclasses."""
        pass

    @abstractmethod
    def _generate_fallback_keys(self, *args: Any) -> list[str]:
        """Generate possible fallback keys from the given arguments.

        This method should return a list of keys in priority order (most specific first).
        The base class will try each key in order and return the first match.

        Args:
            *args: The same arguments passed to _fetch_prompt.

        Returns:
            List of fallback keys to try, in priority order.
        """
        pass

    def _get_fallback_for_args(self, *args: Any) -> Optional[str]:
        """Get fallback prompt for the given arguments using the key generation strategy.

        Args:
            *args: The same arguments passed to _fetch_prompt.

        Returns:
            The fallback prompt string if available, None otherwise.
        """
        # Get possible keys from subclass
        keys = self._generate_fallback_keys(*args)

        # Try each key in order
        for key in keys:
            fallback = self._get_fallback_prompt(key)
            if fallback:
                return fallback

        # Try default fallback as final resort
        default_fallback = self._get_fallback_prompt('default')
        if default_fallback:
            return default_fallback

        return None

    @abstractmethod
    def get_provider(self, *args: Any) -> Callable[[ReadonlyContext], Awaitable[str]]:
        """Get provider function. Must be implemented by subclasses."""
        pass
