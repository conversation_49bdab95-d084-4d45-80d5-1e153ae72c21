import os
from typing import Any, Awaitable, Callable, Optional, cast

from google.adk.agents.readonly_context import ReadonlyContext
from langfuse._client.get_client import get_client
from langfuse.api.resources.prompts.types.prompt import Prompt_Text
from langfuse.model import TextPromptClient

from .base import PromptProvider


class LangfusePrompt(PromptProvider[TextPromptClient]):
    """Langfuse prompt provider with caching, TTL support, and fallback mechanism."""

    def __init__(self, cache_ttl: Optional[int] = None, fallback_prompts: Optional[dict[str, str]] = None):
        """Initialize Langfuse prompt provider.

        Args:
            cache_ttl: Cache TTL in seconds. If None, uses LANGFUSE_CACHE_TTL environment variable.
            fallback_prompts: Dictionary of fallback prompts keyed by prompt name.
        """
        # Use Langfuse-specific environment variable if available
        if cache_ttl is None:
            cache_ttl = int(os.getenv('LANGFUSE_CACHE_TTL', '60'))

        super().__init__(cache_ttl=cache_ttl, fallback_prompts=fallback_prompts)
        self._langfuse = get_client()

    def _get_cache_key(self, *args: Any) -> tuple[str, ...]:
        """Generate cache key from arguments."""
        return tuple(str(arg) for arg in tuple('langfuse_text_prompt') + args)

    async def _fetch_prompt(self, prompt_name: str, label: str | None) -> TextPromptClient:
        """Fetch prompt from Langfuse API."""
        prompt = await self._langfuse.async_api.prompts.get(prompt_name, label=label)
        return TextPromptClient(cast(Prompt_Text, prompt))

    def _generate_fallback_keys(self, prompt_name: str, label: str | None) -> list[str]:
        """Generate possible fallback keys for Langfuse prompts.

        Args:
            prompt_name: The name of the prompt.
            label: The label of the prompt (optional).

        Returns:
            List of fallback keys to try, in priority order.
        """
        keys = []

        # Strategy 1: Most specific - prompt_name with label
        if label:
            keys.append(f'{prompt_name}_{label}')

        # Strategy 2: Less specific - prompt_name only
        keys.append(prompt_name)

        return keys

    def add_fallback_prompt(self, prompt_name: str, prompt_content: str, label: Optional[str] = None) -> None:
        """Add or update a fallback prompt for Langfuse provider.

        Args:
            prompt_name: The name of the prompt.
            prompt_content: The fallback prompt content.
            label: Optional label for the prompt.
        """
        # Generate the appropriate key using the same logic as _generate_fallback_keys
        if label:
            key = f'{prompt_name}_{label}'
        else:
            key = prompt_name

        super().add_fallback_prompt(key, prompt_content)

    def get_provider(
        self, prompt_name: str = 'Next-chat', label: Optional[str] = None
    ) -> Callable[[ReadonlyContext], Awaitable[str]]:
        """Get provider function with the same signature as langfuse_provider."""
        if label is None:
            label = os.getenv('LANGFUSE_PROMPT_LABEL', 'production')

        async def prompt_function(context: ReadonlyContext) -> str:
            """Async function that returns compiled prompt string."""
            prompt_result = await self._get_prompt(context, prompt_name, label)

            # Check if result is already a string (from state)
            if isinstance(prompt_result, str):
                return prompt_result

            # Otherwise it's a TextPromptClient, so compile it
            return prompt_result.compile()

        return prompt_function


# Default fallback prompts for common use cases
DEFAULT_FALLBACK_PROMPTS = {
    'next-chat': """You are a helpful AI assistant. Please provide accurate, helpful, and concise responses to user questions.

Key guidelines:
- Be informative and accurate
- Keep responses clear and well-structured
- Ask for clarification if the question is ambiguous
- Admit when you don't know something""",
    'default': """You are a helpful AI assistant. Please assist the user with their questions and tasks to the best of your ability.

This is the default fallback prompt used when no specific prompt is available.""",
}

langfuse_prompt = LangfusePrompt(fallback_prompts=DEFAULT_FALLBACK_PROMPTS)
