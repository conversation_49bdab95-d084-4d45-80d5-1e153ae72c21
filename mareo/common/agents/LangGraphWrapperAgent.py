# lg_wrapper_agent.py
import uuid
from typing import Any, AsyncGenerator

from google.adk.agents import BaseAgent, InvocationContext
from google.adk.events import (
    Event,
)
from google.adk.models import LlmResponse
from google.genai.types import ModelContent, Part


class LangGraphWrapperAgent(BaseAgent):
    """
    把任何 `compiled_graph`（StateGraph.compile() 的结果）包装成 ADK Agent。

    参数
    ----
    name:             ADK 里 agent 的唯一标识
    compiled_graph:   LangGraph 的 CompiledStateGraph (Runnable)
    output_key:       最终回复在 state 里的键，默认依次尝试 'output' / 'answer'
    stream_modes:     传给 .astream() 的 mode 列表；默认 ['updates']
    """

    def __init__(
        self,
        name: str,
        compiled_graph,
        *,
        output_key: str | None = None,
        stream_modes: list[str] | None = None,
    ):
        super().__init__(name=name)
        self._graph = compiled_graph
        self._output_key = output_key  # optional
        self._stream_modes = stream_modes or ['updates']

    # ---------- 核心：自定义执行逻辑 ----------
    async def _run_async_impl(self, ctx: InvocationContext) -> AsyncGenerator[Event, None]:
        """
        1. 把用户输入和 thread_id 传给 LangGraph
        2. 将 LangGraph 的增量更新映射为 THOUGHT 流
        3. 结束后发出最终 TEXT 事件
        4. 把 thread_id 存回 session.state
        """

        # ==== 0) 线程标识 ====
        thread_id = ctx.session.state.get('lg_thread_id')
        if thread_id is None:
            thread_id = str(uuid.uuid4())
            ctx.session.state['lg_thread_id'] = thread_id  # 首次对话存入

        lg_cfg = {'configurable': {'thread_id': thread_id}}

        # ==== 1) 组织 LangGraph 输入 ====
        # 这里不做 schema 假设，只把 user content 塞进 "input"
        # 如需更复杂映射，自行处理
        lg_inputs: dict[str, Any] = {'input': ctx.user_content.parts[-1].text}

        # ==== 2) 流式跑 LangGraph ====
        final_state: dict[str, Any] = {}
        async for chunk in self._graph.astream(
            lg_inputs,
            stream_mode=self._stream_modes,
            config=lg_cfg,
        ):
            # LangGraph "updates" 形式为 {node_name: {...partial state...}}
            # 我们用 node 名 + 内容做一次简易格式化
            node, update = next(iter(chunk[1].items()))
            msg = f'[{node}] {update}'

            # 推理片段 -> THOUGHT
            model_response_event = Event(
                id=Event.new_id(),
                invocation_id=ctx.invocation_id,
                author=ctx.agent.name,
                branch=ctx.branch,
            )
            llm_response = LlmResponse(content=ModelContent(parts=[Part(text=msg, thought=True)]))
            yield Event.model_validate(
                {
                    **model_response_event.model_dump(exclude_none=True),
                    **llm_response.model_dump(exclude_none=True),
                }
            )

            # 顺便累积最后一次完整状态，方便找最终输出
            final_state.update(update)

        # ==== 3) 取最终回复 ====
        key_order = [self._output_key, 'output', 'answer']
        answer = None
        for k in key_order:
            if k and k in final_state:
                answer = final_state[k]
                break
        if answer is None:  # 兜底
            answer = str(final_state)

        model_response_event = Event(
            id=Event.new_id(),
            invocation_id=ctx.invocation_id,
            author=ctx.agent.name,
            branch=ctx.branch,
        )
        llm_response = LlmResponse(content=ModelContent(parts=[Part(text=answer)]))
        # 给用户的最终回复 -> TEXT
        yield Event.model_validate(
            {
                **model_response_event.model_dump(exclude_none=True),
                **llm_response.model_dump(exclude_none=True),
            }
        )

        # ==== 4) （可选）你想在这里手动持久化别的数据也行 ====
        # 但 thread_id 已经放在 session.state 里了，
        # LangGraph 自己会利用 checkpointer 保存/恢复。
