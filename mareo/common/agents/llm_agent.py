from google.adk.agents.llm_agent import LlmAgent as ADKAgent
from google.adk.flows.llm_flows.auto_flow import AutoFlow
from google.adk.flows.llm_flows.base_llm_flow import BaseLlmFlow
from google.adk.flows.llm_flows.single_flow import SingleFlow


class LlmAgent(ADKAgent):
    @property
    def _llm_flow(self) -> BaseLlmFlow:
        if self.disallow_transfer_to_parent and self.disallow_transfer_to_peers and not self.sub_agents:
            return SingleFlow()
        else:
            return AutoFlow()
