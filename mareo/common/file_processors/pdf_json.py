from __future__ import annotations

import argparse
import json
from html import unescape as html_unescape
from html.parser import <PERSON><PERSON><PERSON>ars<PERSON>
from pathlib import Path
from typing import Iterable


def _wrap_inline_equation(content: str) -> str:
    """Wrap inline equation content using markdown inline math syntax.

    The project guidelines prefer \\( and \\) for inline math.
    """
    # The content may include escaped percent like "20.49\\%" in JSON; keep as-is.
    return f'\\({content}\\)'


def _iter_spans(block: dict) -> Iterable[tuple[str, str]]:
    """Yield (type, content) for spans within a para block, handling nested structures.

    Supports blocks that directly contain 'lines' or nested 'blocks' with 'lines'.
    Span types can include: 'text', 'inline_equation', 'image', 'table'.
    """
    if 'lines' in block:
        for line in block.get('lines', []) or []:
            for span in line.get('spans', []) or []:
                span_type = span.get('type')
                # Prefer explicit content fields depending on type
                if span_type == 'text':
                    content = span.get('content', '')
                elif span_type == 'inline_equation':
                    content = span.get('content', '')
                elif span_type == 'image':
                    # Use image_path if present; otherwise empty
                    content = span.get('image_path') or ''
                elif span_type == 'table':
                    content = span.get('html') or ''
                else:
                    # Unknown span types are skipped if no textual content
                    content = span.get('content') or ''
                yield span_type or 'text', content
    elif 'blocks' in block:
        for sub in block.get('blocks', []) or []:
            # Recurse once: handle typical 'image_body', 'image_caption', etc.
            if 'lines' in sub:
                for line in sub.get('lines', []) or []:
                    for span in line.get('spans', []) or []:
                        span_type = span.get('type')
                        if span_type == 'text':
                            content = span.get('content', '')
                        elif span_type == 'inline_equation':
                            content = span.get('content', '')
                        elif span_type == 'image':
                            content = span.get('image_path') or ''
                        elif span_type == 'table':
                            content = span.get('html') or ''
                        else:
                            content = span.get('content') or ''
                        yield span_type or 'text', content


def _split_into_sentences(text: str) -> list[str]:
    """Split text into sentences for Chinese/English punctuation while preserving end marks.

    Sentence delimiters: Chinese/English variants of exclamation/question/semicolon.
    Note: We intentionally avoid splitting on '.' to preserve decimals, codes (e.g., 000333. SZ), and URLs.
    Any trailing remainder without a terminal mark is treated as one sentence if non-empty.
    """
    if not text:
        return []

    delimiters = {'。', '！', '？', '；', '!', '?', ';'}
    sentences: list[str] = []
    current: list[str] = []
    i = 0
    n = len(text)
    while i < n:
        ch = text[i]
        current.append(ch)
        # Avoid splitting on the leading '!' of markdown images '!['
        if ch == '!' and i + 1 < n and text[i + 1] == '[':
            i += 1
            continue
        if ch in delimiters:
            sentence = ''.join(current).strip()
            if sentence:
                sentences.append(sentence)
            current = []
        i += 1
    # Remainder
    tail = ''.join(current).strip()
    if tail:
        sentences.append(tail)
    return sentences


class _HTMLTableParser(HTMLParser):
    """Minimal HTML table parser to extract rows and cell texts.

    It ignores nested tables and most attributes like colspan/rowspan.
    """

    def __init__(self) -> None:
        super().__init__()
        self._in_td: bool = False
        self._in_th: bool = False
        self._current_cell_parts: list[str] = []
        self._current_row: list[str] = []
        self.rows: list[list[str]] = []

    def handle_starttag(self, tag: str, attrs) -> None:  # noqa: ANN001 - stdlib signature
        if tag.lower() == 'tr':
            self._current_row = []
        elif tag.lower() == 'td':
            self._in_td = True
            self._current_cell_parts = []
        elif tag.lower() == 'th':
            self._in_th = True
            self._current_cell_parts = []

    def handle_endtag(self, tag: str) -> None:  # noqa: ANN001 - stdlib signature
        lower = tag.lower()
        if lower == 'td' and self._in_td:
            text = html_unescape(''.join(self._current_cell_parts)).strip()
            self._current_row.append(text)
            self._in_td = False
            self._current_cell_parts = []
        elif lower == 'th' and self._in_th:
            text = html_unescape(''.join(self._current_cell_parts)).strip()
            self._current_row.append(text)
            self._in_th = False
            self._current_cell_parts = []
        elif lower == 'tr':
            if self._current_row:
                self.rows.append(self._current_row)
            self._current_row = []

    def handle_data(self, data: str) -> None:  # noqa: ANN001 - stdlib signature
        if self._in_td or self._in_th:
            self._current_cell_parts.append(data)


def _sanitize_markdown_cell_text(text: str) -> str:
    """Prepare cell content for markdown table rendering."""
    if not text:
        return ''
    # Replace pipes to avoid breaking the table layout
    sanitized = text.replace('|', '\\|')
    # Collapse internal newlines/whitespace to single spaces
    sanitized = ' '.join(sanitized.split())
    return sanitized


def _html_table_to_markdown(table_html: str) -> str:
    """Convert an HTML <table>...</table> to a GitHub-flavored Markdown table string."""
    parser = _HTMLTableParser()
    try:
        parser.feed(table_html)
    except Exception:
        # If parsing fails, return the original HTML as fallback
        return table_html

    rows = [[_sanitize_markdown_cell_text(c) for c in row] for row in parser.rows]
    if not rows:
        return ''
    num_cols = max((len(r) for r in rows), default=0)
    if num_cols == 0:
        return ''

    # Pad rows to equal column count
    padded_rows: list[list[str]] = [row + [''] * (num_cols - len(row)) for row in rows]

    header = padded_rows[0]
    separator = ['---'] * num_cols
    body = padded_rows[1:] if len(padded_rows) > 1 else []

    def fmt_row(cells: list[str]) -> str:
        return '| ' + ' | '.join(cells) + ' |'

    lines: list[str] = [fmt_row(header), fmt_row(separator)]
    lines.extend(fmt_row(r) for r in body)
    return '\n'.join(lines)


def aggregate_markdown_lines_from_pdf_json(data: dict, file_tag: str) -> list[str]:
    r"""Aggregate PDF-JSON into markdown sentences prefixed with traceable span ids.

    - Prefix format: "{file_tag}::{SPAN_ID}:: ", where SPAN_ID = "p{page_idx}b{block_idx}s{sent_idx}".
      page_idx and block_idx come from the JSON if present (0-based as provided). sent_idx is 1-based.
    - Titles: prepend '#' repeated by their level (default 1) before the title text inside the sentence.
    - Inline equations: wrap with markdown inline math using \\( ... \\).
    - Images: render as markdown image: ![image]({url}) if a path/url is available.
    - Tables: include the HTML table as-is in the sentence body.

    Returns a list of strings, each representing one prefixed sentence/line.
    """
    results: list[str] = []
    pages = data.get('pdf_info', []) or []

    for page in pages:
        page_idx = page.get('page_idx')
        para_blocks = page.get('para_blocks', []) or []

        for i, block in enumerate(para_blocks):
            block_idx = block.get('index', i)
            block_type = block.get('type')

            # Collect textual content by concatenating spans in order
            collected_parts: list[str] = []
            for span_type, content in _iter_spans(block):
                if not content:
                    continue
                if span_type == 'inline_equation':
                    collected_parts.append(_wrap_inline_equation(content))
                elif span_type == 'image':
                    # If it's an image path, render markdown image
                    collected_parts.append(f'![image]({content})')
                elif span_type == 'table':
                    # Convert HTML table to Markdown table
                    md_table = _html_table_to_markdown(content)
                    if md_table:
                        collected_parts.append(f'\n{md_table}\n')
                else:
                    collected_parts.append(content)

            raw_text = ''.join(collected_parts).strip()

            # For title blocks, create a single sentence with heading marks
            if block_type == 'title':
                level = int(block.get('level') or 1)
                level = max(1, min(level, 6))
                title_text = raw_text
                if title_text:
                    sent_idx = 1
                    span_id = f'p{page_idx}b{block_idx}s{sent_idx}'
                    prefix = f'{file_tag}::{span_id}:: '
                    results.append(f'{prefix}{"#" * level} {title_text}')
                continue

            # Split other content types into sentences (including plain text, image-only, tables)
            sentences = _split_into_sentences(raw_text) if raw_text else []

            if not sentences and collected_parts:
                # If there was non-textual content only (e.g., image/table), keep it as one line
                sentences = [''.join(collected_parts)]

            for j, sentence in enumerate(sentences, start=1):
                if not sentence:
                    continue
                span_id = f'p{page_idx}b{block_idx}s{j}'
                prefix = f'{file_tag}::{span_id}:: '
                results.append(f'{prefix}{sentence}')

    return results


def aggregate_markdown_from_pdf_json(data: dict, file_tag: str, join_with: str = '\n') -> str:
    """Convenience wrapper returning a single markdown string from the aggregated lines."""
    lines = aggregate_markdown_lines_from_pdf_json(data, file_tag=file_tag)
    return join_with.join(lines)


def _find_default_example() -> Path | None:
    """Return a default example JSON path if available, otherwise None."""
    examples_dir = Path(__file__).parent / 'examples'
    # Prefer the known example filename if present
    known = examples_dir / (
        '80 2025Q1 C端+17%，B端+25%  2025-06-29-长江证券-C端与B端共振，营收、业绩实现高增-13547_MinerU__20250813051348.json'
    )
    if known.exists():
        return known
    # Fallback: pick the first .json under examples
    for path in sorted(examples_dir.glob('*.json')):
        return path
    return None


def tmp() -> str:
    return aggregate_markdown_from_pdf_json(json.loads(Path(_find_default_example()).read_text()), file_tag='S1')


def main() -> None:
    """CLI entry for quick testing: read JSON and print aggregated Markdown."""
    default_example = _find_default_example()
    parser = argparse.ArgumentParser(description='Aggregate MinerU-like PDF JSON into traceable Markdown.')
    parser.add_argument(
        '-i',
        '--input',
        type=str,
        default=str(default_example) if default_example else None,
        help='Path to input JSON file (defaults to bundled example if available).',
    )
    parser.add_argument('-t', '--tag', type=str, default='S1', help='File tag prefix (e.g., S1, S2).')
    args = parser.parse_args()

    if not args.input:
        raise SystemExit('No input JSON provided and no example found.')

    input_path = Path(args.input)
    if not input_path.exists():
        raise SystemExit(f'Input file not found: {input_path}')

    with input_path.open('r', encoding='utf-8') as f:
        data = json.load(f)

    output = aggregate_markdown_from_pdf_json(data, file_tag=args.tag)
    print(output)


if __name__ == '__main__':
    main()
