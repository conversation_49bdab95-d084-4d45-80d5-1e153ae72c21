{"name": "statement", "strict": true, "schema": {"type": "object", "required": ["value", "claims"], "properties": {"value": {"type": "number"}, "claims": {"type": "array", "minItems": 1, "items": {"type": "object", "required": ["level", "kind", "text", "confidence"], "properties": {"level": {"type": "string", "enum": ["0", "1", "2", "3"]}, "kind": {"type": "string", "enum": ["quote", "paraphrase", "derived", "insight", "gap"]}, "text": {"type": "string"}, "confidence": {"type": "number", "minimum": 0, "maximum": 1}, "evidence": {"type": "array", "items": {"type": "object", "required": ["source_id", "span_id"], "properties": {"source_id": {"type": "string"}, "span_id": {"type": "string"}}, "additionalProperties": false}}, "derived_from": {"type": ["object", "null"], "properties": {"basis_claims_inline": {"type": "array", "items": {"type": "object", "properties": {"ref": {"type": "string"}}, "additionalProperties": false}}, "reasoning": {"type": "string"}, "validation_needed": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "additionalProperties": false}}}, "additionalProperties": false}}