from __future__ import annotations

from typing import Annotated, Literal, Optional

from pydantic import BaseModel, ConfigDict, Field


class Evidence(BaseModel):
    """Evidence item linking a claim to a source span."""

    model_config = ConfigDict(extra='forbid')

    source_id: str
    span_id: str


class BasisClaimRef(BaseModel):
    """Inline reference to a basis claim by id/string ref."""

    model_config = ConfigDict(extra='forbid')

    ref: str


class DerivedFrom(BaseModel):
    """Structure describing derivation details of a claim."""

    model_config = ConfigDict(extra='forbid')

    basis_claims_inline: Optional[list[BasisClaimRef]] = None
    reasoning: Optional[str] = None
    validation_needed: Optional[list[str]] = None


class Claim(BaseModel):
    """A specific claim associated with a statement."""

    model_config = ConfigDict(extra='forbid')

    level: Literal['0', '1', '2', '3']
    kind: Literal['quote', 'paraphrase', 'derived', 'insight', 'gap']
    text: str
    confidence: Annotated[float, Field(ge=0.0, le=1.0)]
    evidence: Optional[list[Evidence]] = None
    derived_from: Optional[DerivedFrom] = None


class Statement(BaseModel):
    """A statement in a section, containing at least one claim."""

    model_config = ConfigDict(extra='forbid')

    text: str
    claims: Annotated[list[Claim], Field(min_length=1)]


class Result(BaseModel):
    """A statement in a section, containing at least one claim."""

    model_config = ConfigDict(extra='forbid')

    value: str = Field(
        description="The result value. Put only the necessary value as in a sheet cell, don't put any other text."
    )
    claims: Annotated[list[Claim], Field(min_length=1)]


class Section(BaseModel):
    """A section with a name and a list of statements."""

    model_config = ConfigDict(extra='forbid')

    name: str
    statements: list[Statement]


class SourceManifestItem(BaseModel):
    """Entry describing a source participating in the report."""

    model_config = ConfigDict(extra='forbid')

    source_id: str
    content_hash: str


class Limits(BaseModel):
    """Constraints and guardrails for report composition/analysis."""

    model_config = ConfigDict(extra='forbid')

    max_L0_ratio: Optional[float] = None
    no_L0_in: Optional[list[str]] = None


class Meta(BaseModel):
    """Metadata including source manifest and optional limits."""

    model_config = ConfigDict(extra='forbid')

    source_manifest: Optional[list[SourceManifestItem]] = None
    limits: Optional[Limits] = None


class Report(BaseModel):
    """Top-level report model matching the provided JSON schema."""

    model_config = ConfigDict(extra='forbid')

    report_title: str
    sections: list[Section]
    meta: Optional[Meta] = None
