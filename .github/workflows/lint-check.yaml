name: lint check
on:
  workflow_call:

jobs:
  ruff:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: tj-actions/changed-files@v46
        id: changed-files
        with:
          files: |
            **.py
            **.pyi

      - uses: astral-sh/ruff-action@v3
        if: ${{ steps.changed-files.outputs.all_changed_files }}
        with:
          version-file: './pyproject.toml'
          args: check --config dev_config/ruff.toml
          src: ${{ steps.changed-files.outputs.all_changed_files }}
