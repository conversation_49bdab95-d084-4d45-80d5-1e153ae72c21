name: Deploy to Kubernetes with Environment
on:
  workflow_call:
    inputs:
      deploy_env:
        required: true
        description: The environment to deploy to k8s
        type: string
      url_prefix:
        required: true
        description: The URL prefix for the environment, @ for main
        type: string
      url_base:
        required: false
        description: The URL for the environment
        type: string
        default: 'next.mareo.ai'
      image_name:
        required: true
        description: The name of the image to deploy
        type: string
      image_tag:
        required: true
        description: The tag of the image to deploy
        type: string
      force_restart:
        required: false
        description: Force restart the deployment even if the image tag is the same
        type: boolean
        default: false

jobs:
  deploy_to_k8s:
    environment:
      name: ${{ inputs.deploy_env }}
      url: https://${{ inputs.url_prefix=='@' && inputs.url_base || format('{0}.{1}', inputs.url_prefix, inputs.url_base) }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Kubeconfig
        run: |
          mkdir -p $HOME/.kube/
          echo "${{ secrets.KUBE_CONFIG }}" | base64 -d > $HOME/.kube/config
          chmod 600 $HOME/.kube/config
          kubectl version

      - name: Set k8s reusable env vars
        run: |
          NAMESPACE="${{ inputs.deploy_env=='dev' && 'mareo-next-dev' || 'mareo-next' }}"
          if [ "${{ inputs.url_prefix }}" == "@" ]; then
            DEPLOY_URL="${{ inputs.url_base }}"
            PR_SUFFIX="";
          else 
            DEPLOY_URL="${{ inputs.url_prefix }}.${{ inputs.url_base }}"
            PR_SUFFIX="-${{ inputs.url_prefix }}"
          fi
          MAREO_APP_NAME=mareo${PR_SUFFIX}
          MAREO_SVC_NAME=mareo-svc${PR_SUFFIX}
          MAREO_ING_NAME=mareo-ing${PR_SUFFIX}
          MAREO_APP_IMAGE=${{ vars.CR_URL_VPC }}/${{ github.repository_owner }}/${{ inputs.image_name }}:${{ inputs.image_tag }}

          echo "NAMESPACE=$NAMESPACE" >> $GITHUB_ENV
          echo "DEPLOY_URL=$DEPLOY_URL" >> $GITHUB_ENV
          echo "PR_SUFFIX=$PR_SUFFIX" >> $GITHUB_ENV
          echo "MAREO_APP_NAME=$MAREO_APP_NAME" >> $GITHUB_ENV
          echo "MAREO_SVC_NAME=$MAREO_SVC_NAME" >> $GITHUB_ENV
          echo "MAREO_ING_NAME=$MAREO_ING_NAME" >> $GITHUB_ENV
          echo "MAREO_APP_IMAGE=$MAREO_APP_IMAGE" >> $GITHUB_ENV

      - name: Set up k8s vars and secrets
        run: |
          echo "Setting up mareo-configmap.yaml and mareo-secret.yaml..."
          bash .github/scripts/setup-env.sh '${{ toJSON(vars) }}' k8s/dev-pipe/mareo-configmap.yaml "env"
          bash .github/scripts/setup-env.sh '${{ toJSON(secrets) }}' k8s/dev-pipe/mareo-secret.yaml "secret"

      - name: Set up cleanable k8s YAML Configurations(deployment, service, ingress)
        run: |
          echo "Setting up mareo-deployment.yaml..."
          yq -i '.metadata.labels.app=strenv(MAREO_APP_NAME)' k8s/dev-pipe/mareo-deployment.yaml
          yq -i '.metadata.name=strenv(MAREO_APP_NAME)' k8s/dev-pipe/mareo-deployment.yaml
          yq -i '.spec.selector.matchLabels.app=strenv(MAREO_APP_NAME)' k8s/dev-pipe/mareo-deployment.yaml
          yq -i '.spec.template.metadata.labels.app=strenv(MAREO_APP_NAME)' k8s/dev-pipe/mareo-deployment.yaml
          yq -i '(.spec.template.spec.containers[] | select(.name == "mareo") | .env.[] | select(.name =="aliyun_logs_mareo-next")).name = "aliyun_logs_mareo-next-dev"' k8s/dev-pipe/mareo-deployment.yaml
          yq -i '(.spec.template.spec.containers[] | select(.name == "mareo")).image = strenv(MAREO_APP_IMAGE)' k8s/dev-pipe/mareo-deployment.yaml
          cat k8s/dev-pipe/mareo-deployment.yaml

          echo "Setting up mareo-svc.yaml..."
          yq -i '.metadata.name=strenv(MAREO_SVC_NAME)' k8s/dev-pipe/mareo-svc.yaml
          yq -i '.spec.selector.app=strenv(MAREO_APP_NAME)' k8s/dev-pipe/mareo-svc.yaml
          cat k8s/dev-pipe/mareo-svc.yaml

          echo "Setting up mareo-ing.yaml..."
          yq -i '.metadata.annotations={"alb.ingress.kubernetes.io/listen-ports": "[{\"HTTPS\": 443}]"}' k8s/dev-pipe/mareo-ing.yaml
          yq -i '.metadata.name=strenv(MAREO_ING_NAME)' k8s/dev-pipe/mareo-ing.yaml
          yq -i '.spec.rules[0].host=strenv(DEPLOY_URL)' k8s/dev-pipe/mareo-ing.yaml
          yq -i '.spec.rules[0].http.paths[0].backend.service.name=strenv(MAREO_SVC_NAME)' k8s/dev-pipe/mareo-ing.yaml
          cat k8s/dev-pipe/mareo-ing.yaml

      - name: Make sure imagePullPolicy is set to Always for force-restart
        if: ${{ inputs.force_restart }}
        run: yq -i '(.spec.template.spec.containers[] | select(.name == "mareo")).imagePullPolicy = "Always"' k8s/dev-pipe/mareo-deployment.yaml

      - name: Apply yaml to k8s cluster
        run: kubectl -n ${NAMESPACE} apply -f k8s/dev-pipe/

      - name: Force restart deployment if needed
        if: ${{ inputs.force_restart }}
        run: |
          echo "Forcing restart of deployment ${MAREO_APP_NAME} in namespace ${NAMESPACE}..."
          kubectl -n ${NAMESPACE} rollout restart deployment/${MAREO_APP_NAME}

#      - name: Wait for deployment to be ready
#        run: |
#          kubectl -n ${NAMESPACE} rollout status deployment/${MAREO_APP_NAME} --timeout=300s
#          kubectl -n ${NAMESPACE} get pods -l app=${MAREO_APP_NAME}
