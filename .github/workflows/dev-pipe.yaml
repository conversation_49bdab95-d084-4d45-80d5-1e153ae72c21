name: Development CI/CD Pipeline

permissions:
  pull-requests: read
  contents: read

on:
  pull_request:
    branches:
      - main
  push:
    branches:
      - main
    tags:
      - 'release-v[0-9]+.[0-9]+.[0-9]+.?*'
      - 'hotfix-v[0-9]+.[0-9]+.[0-9]+.?*'

# If triggered by a PR, it will be in the same group. However, each commit on main will be in its own unique group
concurrency:
  group: ${{ github.workflow }}-${{ (github.head_ref && github.ref) || github.run_id }}
  cancel-in-progress: true

jobs:
  changes:
    name: Detect File Changes
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v3
        id: filter
        with:
          # TODO: Remove frontend/** when frontend is decoupled
          filters: |
            mareo:
              - 'containers/mareo/**'
              - 'mareo/**'
              - 'poetry.lock'
              - 'frontend/**'
    outputs:
      mareo: ${{ steps.filter.outputs.mareo }}

  lint-check:
    name: Lint <PERSON>
    needs: [ changes ]
    if: ${{ needs.changes.outputs.mareo == 'true' }}
    uses: ./.github/workflows/lint-check.yaml

  pytest:
    name: Run pytest
    needs: [ lint-check ]
    uses: ./.github/workflows/pytest.yaml
    secrets: inherit

  build_mareo:
    name: Build Mareo Image
    needs: [ pytest ]
    permissions:
      packages: write
      pull-requests: read
      contents: read
    if: ${{ needs.changes.outputs.mareo == 'true' }}
    uses: ./.github/workflows/build.yaml
    secrets: inherit
    with:
      dockerfile-path: 'containers/mareo/Dockerfile'
      image-name: 'mareo'
      context-path: '.'
      tags: |
        ${{ github.sha }}
        ${{ github.event_name == 'push' && github.ref_name || github.event_name == 'pull_request' && format('pr-{0}', github.event.pull_request.number ) || '' }}

  deploy_to_k8s_via_pr:
    name: Deploy Mareo to K8s for PR
    needs: [ build_mareo ]
    if: ${{ github.event_name == 'pull_request' }}
    uses: ./.github/workflows/deploy-to-k8s.yaml
    with:
      deploy_env: 'dev'
      url_prefix: 'pr-${{ github.event.pull_request.number }}'
      image_name: 'mareo'
      image_tag: '${{ github.sha }}'
    secrets: inherit

  deploy_to_k8s_via_main:
    name: Deploy Mareo to K8s for main
    needs: [ build_mareo ]
    if: ${{ github.event_name == 'push' && github.ref_type == 'branch' }}
    uses: ./.github/workflows/deploy-to-k8s.yaml
    with:
      deploy_env: 'main'
      url_prefix: '@'
      image_name: 'mareo'
      image_tag: '${{ github.sha }}'
    secrets: inherit

  deploy_to_k8s_via_tag:
    name: Deploy Mareo to K8s for release tag
    needs: [ build_mareo ]
    if: ${{ github.event_name == 'push' && github.ref_type == 'tag' }}
    uses: ./.github/workflows/deploy-to-k8s.yaml
    with:
      deploy_env: 'main' # Should be a prod env in the future
      url_prefix: '@'
      image_name: 'mareo'
      image_tag: '${{ github.ref_name }}'
    secrets: inherit
