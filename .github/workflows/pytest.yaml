name: Pytest Workflow
on:
  workflow_call:

jobs:
  pytest:
    env:
      PYTHON_VERSION: '3.13.5'
      POETRY_VERSION: '2.1.2'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: packetcoders/action-setup-cache-python-poetry@main
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          poetry-version: ${{ env.POETRY_VERSION }}
          install-args: '--without dev'

      - name: Run pytest
        env:
          LANGFUSE_PUBLIC_KEY: ${{ secrets.LANGFUSE_PUBLIC_KEY }}
          LANGFUSE_SECRET_KEY: ${{ secrets.LANGFUSE_SECRET_KEY }}
          OPENROUTER_API_KEY: ${{ secrets.OPENROUTER_API_KEY }}
          TAVILY_KEY: ${{ secrets.TAVILY_KEY }}
          LANGFUSE_HOST: ${{ vars.LANGFUSE_HOST }}
          TAVILY_BASE: ${{ vars.TAVILY_BASE }}
        run: poetry run pytest
