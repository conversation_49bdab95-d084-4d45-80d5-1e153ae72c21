name: Manually Triggered Redeploy to K8s
on:
  workflow_dispatch:
    inputs:
      deploy_env:
        description: 'Environment to deploy to'
        type: choice
        required: true
        default: dev
        options:
          - dev
          - main
      url_prefix:
        description: 'URL prefix for the deployment, @ for main'
        type: string
        required: true
      image_name:
        description: 'image name to redeploy'
        type: string
        required: false
        default: "mareo"
      image_tag:
        description: 'image tag to redeploy'
        type: string
        required: true

jobs:
  manually_triggered_redeploy:
    name: Deploy Mareo to K8s for main
    uses: ./.github/workflows/deploy-to-k8s.yaml
    with:
      deploy_env: ${{ inputs.deploy_env }}
      url_prefix: ${{ inputs.url_prefix }}
      image_name: ${{ inputs.image_name }}
      image_tag: ${{ inputs.image_tag }}
      force_restart: true
    secrets: inherit
