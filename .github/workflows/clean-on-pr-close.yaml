name: Clean Unneeded k8s Resources and deployments on PR Close
on:
  pull_request:
    types:
      - closed
  workflow_dispatch:
    inputs:
      pr_number:
        description: 'Pull Request Number to clean up resources for'
        type: number
        required: true
env:
  NAMESPACE: 'mareo-next-dev'

jobs:
  clean_on_pr_close:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Kubeconfig
        run: |
          mkdir -p $HOME/.kube/
          echo "${{ secrets.KUBE_CONFIG }}" | base64 -d > $HOME/.kube/config
          chmod 600 $HOME/.kube/config
          kubectl version

      - name: Clean up k8s resources
        run: |
          PR_NUMBER=${{ github.event.pull_request.number || (github.event_name=='workflow_dispatch' && github.event.inputs.pr_number) }}
          kubectl -n ${{ env.NAMESPACE }} delete ingress mareo-ing-pr-${PR_NUMBER} || true
          kubectl -n ${{ env.NAMESPACE }} delete service mareo-svc-pr-${PR_NUMBER} || true
          kubectl -n ${{ env.NAMESPACE }} delete deployment mareo-pr-${PR_NUMBER} || true
