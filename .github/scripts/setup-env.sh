#!/bin/bash

json_str="$1"
yaml_file="$2"
setup_type="$3"

if [[ -z "$json_str" || -z "$yaml_file" || -z "$setup_type" ]]; then
  echo "Usage: $0 <json_string> <yaml_file> <setup_type>"
  echo "setup_type should be either 'env' or 'secret'."
  exit 1
fi

unneeded_keys=("github_token" "KUBE_CONFIG" "CR_URL" "CR_URL_VPC" "CR_USER" "CR_PASS")

echo "${json_str}"| jq -c 'to_entries[]' | while read -r item; do
  # get kv-pair from json
  key=$(echo "$item" | jq -r '.key')
  value=$(echo "$item" | jq -r '.value')

  # Skip unneeded keys
  skip=false
  for unneeded_key in "${unneeded_keys[@]}"; do
    if [[ "$key" == "$unneeded_key" ]]; then
      skip=true
      break
    fi
  done
  if $skip; then
    continue
  fi

  if [[ "$setup_type" == "secret" ]]; then
    value=$(echo -n "$value" | base64 -w0)
  fi

  KEY="$key" VALUE="$value" yq -i '.data[strenv(KEY)] = strenv(VALUE)' "$yaml_file"
done
