# Mareo Agent 项目技术文档

## 📋 项目概述

Mareo Agent 是一个基于 Google ADK (Agent Development Kit) 构建的多智能体协作平台，专注于提供可溯源的AI服务。项目采用微服务架构，支持多种LLM模型，并通过MCP (Model Context Protocol) 集成外部工具和服务。

## 🏗️ 技术架构

### 核心技术栈

```
┌─────────────────────────────────────────────────────────────┐
│                    🌐 前端层 (Frontend)                      │
├─────────────────────┬───────────────────────────────────────┤
│  📱 Dev UI          │  📄 PDF Frontend                     │
│  (Angular + ADK)    │  (React + PDF.js + Tailwind)         │
└─────────────────────┴───────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   📡 API 网关层 (FastAPI)                   │
├─────────────────────┬─────────────────┬───────────────────────┤
│  🤖 Agent API      │  💾 Session API │  📊 Evaluation API   │
└─────────────────────┴─────────────────┴───────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   🎯 Agent Hub (核心层)                     │
├─────────────────────┬─────────────────┬───────────────────────┤
│  💬 Chat Agent     │  🔍 Search Agent│  📋 Report Agent     │
│  🌐 HTML Agent     │  🧠 Think Agent │  ❓ Follow-up Agent  │
└─────────────────────┴─────────────────┴───────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   🔧 MCP 工具层 (Tools)                     │
├─────────────────────┬───────────────────────────────────────┤
│  🔍 Tavily MCP     │  🗄️ Supabase MCP                     │
│  (Web Search)       │  (Database Operations)                │
└─────────────────────┴───────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   🧠 LLM 服务层 (Models)                    │
├─────────────────────┬─────────────────┬───────────────────────┤
│  🌐 OpenRouter      │  🤖 Vertex AI   │  📈 Langfuse         │
│  (Multi-LLM)        │  (Gemini)       │  (Observability)      │
└─────────────────────┴─────────────────┴───────────────────────┘
```

### 系统架构特点

- **🔄 异步处理**: 基于 FastAPI 的异步架构，支持高并发
- **🎯 模块化设计**: Agent Hub 采用插件化架构，易于扩展
- **📊 可观测性**: 集成 Langfuse 和 OpenTelemetry 进行全链路追踪
- **🔗 MCP 集成**: 通过 Model Context Protocol 集成外部工具
- **💾 状态管理**: 支持会话持久化和工件存储

## 🤖 Agent 生态系统

### 核心 Agent 列表

| Agent | 模型 | 功能描述 | 主要用途 |
|-------|------|----------|----------|
| 💬 **Chat Agent** | Qwen3-235B | 主聊天代理，负责用户交互 | 对话管理、任务分发 |
| 🔍 **Search Agent** | Qwen3-235B | 网络搜索代理 | 实时信息检索 |
| 🌐 **HTML Artifact Agent** | Claude Sonnet4 | HTML工件生成 | 网页创建、可视化 |
| 📋 **Report Agent** | Gemini 2.5 Pro | 可溯源报告生成 | 结构化报告、证据链 |
| 🧠 **Thinking Agent** | R1-0528 | 推理思考代理 | 复杂推理、思维链 |
| 🗄️ **Supabase Web App Agent** | Claude Sonnet4 | 数据库应用构建 | 全栈应用生成 |
| 🔄 **LangGraph Demo Agent** | - | 工作流演示 | 复杂流程编排 |
| ❓ **Follow-up Agent** | Gemini Flash | 后续问题生成 | 对话延续、引导 |

### Agent 协作模式

#### 任务分析的代码实现

任务分析的核心逻辑并非通过硬编码的条件判断，而是通过 **智能 Prompt 工程** 和 **Sub-Agent 架构** 实现的：

<augment_code_snippet path="mareo/agent_hub/chat_agent/agent.py" mode="EXCERPT">
```python
def create_chat_agent() -> LlmAgent:
    """Factory method to create a new chat agent instance."""
    return LlmAgent(
        name='Mareo',
        model=qwen3_235b,  # 使用 Qwen3-235B 模型
        description='Agent to chat with user',
        instruction=langfuse_prompt.get_provider(
            prompt_name='Next-chat',
            label=os.getenv('LANGFUSE_PROMPT_LABEL', 'production')
        ),
        # 关键：注册子代理，LLM 会自动决策何时委托
        sub_agents=[create_search_agent(), create_html_artifact_agent()],
    )
```
</augment_code_snippet>

**任务分析机制说明：**

1. **Prompt 驱动决策**: Chat Agent 通过 Langfuse 管理的 `Next-chat` prompt 获得任务分析能力
2. **Sub-Agent 自动发现**: 注册的 `sub_agents` 会被 LLM 自动识别为可用工具
3. **智能委托**: LLM 根据用户意图和可用的 sub-agents 自动决定是否需要委托

<augment_code_snippet path="mareo/common/prompts/langfuse_provider.py" mode="EXCERPT">
```python
# 默认的任务分析 Prompt（当 Langfuse 不可用时的后备）
DEFAULT_FALLBACK_PROMPTS = {
    'next-chat': """You are a helpful AI assistant. Please provide accurate, helpful, and concise responses to user questions.

Key guidelines:
- Be informative and accurate
- Keep responses clear and well-structured
- Ask for clarification if the question is ambiguous
- Admit when you don't know something""",
}

async def prompt_function(context: ReadonlyContext) -> str:
    """动态获取 Prompt，支持实时更新"""
    prompt_result = await self._get_prompt(context, prompt_name, label)
    return prompt_result.compile()  # 编译为最终的指令文本
```
</augment_code_snippet>

**智能决策流程：**
- 🧠 **LLM 分析**: Qwen3-235B 根据 prompt 指令分析用户意图
- 🔍 **工具发现**: 自动识别可用的 sub-agents (search, html_artifact)
- ⚖️ **决策权衡**: 基于任务复杂度和工具能力进行智能委托
- 🔄 **动态执行**: 无需硬编码规则，完全由 LLM 驱动决策

```mermaid
graph TD
    %% 用户交互层
    User[👤 用户] --> ChatAgent[💬 Chat Agent<br/>主控代理]

    %% 主控代理决策
    ChatAgent --> Decision{🤔 任务分析}

    %% 搜索分支
    Decision -->|需要搜索| SearchDelegate[🔄 委托搜索]
    SearchDelegate --> SearchAgent[🔍 Search Agent<br/>Qwen3-235B]
    SearchAgent --> TavilyMCP[🌐 Tavily MCP]
    TavilyMCP --> SearchResults[📊 搜索结果]
    SearchResults --> BackToChat1[🔄 返回主控]

    %% 报告生成分支
    Decision -->|生成报告| ReportDelegate[🔄 委托报告]
    ReportDelegate --> ReportAgent[📋 Report Agent<br/>Gemini 2.5 Pro]
    ReportAgent --> EvidencePool[📚 证据池]
    ReportAgent --> StructuredOutput[🏗️ 结构化输出]
    StructuredOutput --> BackToChat2[🔄 返回主控]

    %% HTML工件分支
    Decision -->|创建网页| HTMLDelegate[🔄 委托HTML]
    HTMLDelegate --> HTMLAgent[🌐 HTML Agent<br/>Claude Sonnet4]
    HTMLAgent --> GenerateHTML[📝 生成HTML]
    GenerateHTML --> SaveArtifact[💾 保存工件]
    SaveArtifact --> BackToChat3[🔄 返回主控]

    %% 数据库应用分支
    Decision -->|构建应用| SupabaseDelegate[🔄 委托数据库]
    SupabaseDelegate --> SupabaseAgent[🗄️ Supabase Agent<br/>Claude Sonnet4]
    SupabaseAgent --> SupabaseMCP[🗄️ Supabase MCP]
    SupabaseMCP --> DatabaseOps[🔧 数据库操作]
    DatabaseOps --> WebAppGen[🌐 Web应用生成]
    WebAppGen --> BackToChat4[🔄 返回主控]

    %% 复杂推理分支
    Decision -->|深度思考| ThinkingDelegate[🔄 委托思考]
    ThinkingDelegate --> ThinkingAgent[🧠 Thinking Agent<br/>R1-0528]
    ThinkingAgent --> ReasoningChain[🔗 推理链]
    ReasoningChain --> BackToChat5[🔄 返回主控]

    %% 工作流分支
    Decision -->|复杂流程| LangGraphDelegate[🔄 委托工作流]
    LangGraphDelegate --> LangGraphAgent[🔄 LangGraph Agent]
    LangGraphAgent --> WorkflowExecution[⚙️ 工作流执行]
    WorkflowExecution --> BackToChat6[🔄 返回主控]

    %% 结果汇总和后续
    BackToChat1 --> ResponseGen[💭 响应生成]
    BackToChat2 --> ResponseGen
    BackToChat3 --> ResponseGen
    BackToChat4 --> ResponseGen
    BackToChat5 --> ResponseGen
    BackToChat6 --> ResponseGen

    %% 后续问题生成
    ResponseGen --> FollowUpCheck{❓ 需要后续?}
    FollowUpCheck -->|是| FollowUpAgent[❓ Follow-up Agent<br/>Gemini Flash]
    FollowUpCheck -->|否| FinalResponse[📤 最终响应]

    FollowUpAgent --> SuggestQuestions[💡 建议问题]
    SuggestQuestions --> FinalResponse

    %% 监控和存储
    ChatAgent --> Monitoring[📊 监控追踪]
    SearchAgent --> Monitoring
    ReportAgent --> Monitoring
    HTMLAgent --> Monitoring
    SupabaseAgent --> Monitoring
    ThinkingAgent --> Monitoring
    LangGraphAgent --> Monitoring
    FollowUpAgent --> Monitoring

    Monitoring --> Langfuse[📈 Langfuse]
    Monitoring --> OpenTelemetry[📊 OpenTelemetry]

    FinalResponse --> SessionStore[(💾 会话存储)]
    SaveArtifact --> ArtifactStore[(📦 工件存储)]

    %% 样式定义
    classDef user fill:#e1f5fe
    classDef mainAgent fill:#e8f5e8
    classDef subAgent fill:#fce4ec
    classDef decision fill:#fff3e0
    classDef process fill:#f3e5f5
    classDef mcp fill:#f1f8e9
    classDef storage fill:#fafafa
    classDef monitoring fill:#e0f2f1

    class User user
    class ChatAgent mainAgent
    class SearchAgent,ReportAgent,HTMLAgent,SupabaseAgent,ThinkingAgent,LangGraphAgent,FollowUpAgent subAgent
    class Decision,FollowUpCheck decision
    class SearchDelegate,ReportDelegate,HTMLDelegate,SupabaseDelegate,ThinkingDelegate,LangGraphDelegate,ResponseGen,GenerateHTML,DatabaseOps,WebAppGen,ReasoningChain,WorkflowExecution,SuggestQuestions process
    class TavilyMCP,SupabaseMCP mcp
    class SessionStore,ArtifactStore storage
    class Monitoring,Langfuse,OpenTelemetry monitoring
```

## 🔧 MCP 工具集成

### MCP (Model Context Protocol) 简介

MCP 是一个标准化协议，允许 AI 模型与外部工具和服务进行安全、结构化的交互。Mareo 项目集成了两个关键的 MCP 工具：

#### 🔍 Tavily Search MCP

**核心实现代码：**

<augment_code_snippet path="mareo/common/tools/mcp/tavily.py" mode="EXCERPT">
```python
def get_tavily_toolset() -> MCPToolset:
    """Create a new tavily toolset instance."""
    if not (tavily_key := os.getenv('TAVILY_KEY', '')):
        logging.warning('Env var TAVILY_KEY is not set')
    return MCPToolset(
        connection_params=StreamableHTTPServerParams(url=os.getenv('TAVILY_BASE', '')),
        auth_scheme=APIKey(name='s', **{'in': 'header'}),
        auth_credential=AuthCredential(auth_type=AuthCredentialTypes.API_KEY, api_key=tavily_key),
    )
```
</augment_code_snippet>

**使用场景：**

<augment_code_snippet path="mareo/agent_hub/search_agent/agent.py" mode="EXCERPT">
```python
def create_search_agent() -> LlmAgent:
    """Factory method to create a new search agent instance."""
    tavily_key = os.getenv('TAVILY_KEY', '')
    model = get_qwen3_235b(tool_choice='required')
    return LlmAgent(
        name='mareo_search',
        model=model,
        description='Agent to search information online',
        instruction='You are a search agent to fetch information online...',
        tools=[get_tavily_toolset()] if tavily_key else [],  # 🔍 集成 Tavily MCP
    )
```
</augment_code_snippet>

**技术特点：**
- **连接方式**: HTTP Server (StreamableHTTPServerParams)
- **认证机制**: API Key Header 认证
- **数据流**: 实时流式搜索结果
- **用途**: 为 Search Agent 提供网络搜索能力

#### 🗄️ Supabase MCP

**核心实现代码：**

<augment_code_snippet path="mareo/common/tools/mcp/supabase.py" mode="EXCERPT">
```python
def get_supabase_toolset() -> MCPToolset:
    """Create a Supabase MCP toolset instance using local stdio via npx."""
    project_ref = os.getenv('SUPABASE_PROJECT_REF', '')
    access_token = os.getenv('SUPABASE_ACCESS_TOKEN', '')
    read_only_flag = os.getenv('SUPABASE_MCP_READ_ONLY', '1').lower() in {'1', 'true', 'yes'}

    args = ['-y', '@supabase/mcp-server-supabase@latest']
    if read_only_flag:
        args.append('--read-only')  # 🔒 默认只读模式
    if project_ref:
        args.extend(['--project-ref', project_ref])

    env = {}
    if access_token:
        env['SUPABASE_ACCESS_TOKEN'] = access_token

    return MCPToolset(
        connection_params=StdioServerParameters(
            command='npx',  # 🚀 通过 npx 启动 MCP 服务器
            args=args,
            env=env or None,
        )
    )
```
</augment_code_snippet>

**使用场景：**

<augment_code_snippet path="mareo/agent_hub/supabase_web_app_agent/agent.py" mode="EXCERPT">
```python
def create_supabase_web_app_agent() -> LlmAgent:
    """Factory to create a sub-agent that builds a minimal Supabase-backed web app page."""
    instruction = (
        'You are a web app builder using Supabase via MCP. '
        'On transfer, do the following via tools: '
        '1) Use Supabase MCP tools to create necessary tables, insert a sample row if empty, and select up to 10 rows. '
        '2) Build a single-page HTML web app using the database as backend, create the page by calling `save_webpage_html`. '
    )

    return LlmAgent(
        name='supabase_web_app_agent',
        model=claude_sonnet4,
        description='Agent to build a Supabase-backed web app page via MCP and save as artifact',
        instruction=instruction,
        tools=[get_supabase_toolset(), save_webpage_html],  # 🗄️ 集成 Supabase MCP
    )
```
</augment_code_snippet>

**技术特点：**
- **连接方式**: Stdio (StdioServerParameters)
- **启动机制**: 通过 npx 动态启动 MCP 服务器
- **安全模式**: 默认启用只读模式 (--read-only)
- **环境隔离**: 独立的环境变量传递
- **用途**: 为 Supabase Agent 提供数据库操作能力

### MCP 架构优势

1. **🔌 标准化接口**: 统一的工具集成方式，易于扩展
2. **🔒 安全隔离**: 每个 MCP 工具运行在独立的进程中
3. **⚡ 高性能**: 支持流式数据传输和异步操作
4. **🛠️ 易于维护**: 工具逻辑与 Agent 逻辑分离
5. **🔄 动态加载**: 根据环境变量动态启用/禁用工具

## 📊 可溯源报告系统

### RAG 流程架构

```mermaid
flowchart TD
    %% 文档输入层
    PDF[📄 PDF 文档] --> MinerU[⚙️ MinerU 解析器]
    MinerU --> StructData[📊 结构化数据<br/>JSON格式]

    %% 证据池构建
    StructData --> TextBlocks[📝 文本块提取]
    StructData --> BBoxInfo[📐 坐标信息提取]
    StructData --> PageInfo[📄 页面信息提取]

    TextBlocks --> EvidencePool[🔍 证据池<br/>Evidence Pool]
    BBoxInfo --> EvidencePool
    PageInfo --> EvidencePool

    %% 查询处理
    UserQuery[❓ 用户查询] --> QueryAnalysis[🔍 查询分析]
    QueryAnalysis --> EvidenceRetrieval[📚 证据检索]
    EvidenceRetrieval --> EvidencePool

    %% 报告生成
    EvidencePool --> RelevantEvidence[🎯 相关证据片段]
    RelevantEvidence --> ReportAgent[📋 Report Agent<br/>Gemini 2.5 Pro]

    %% Schema 驱动生成
    JSONSchema[📊 JSON Schema] --> ReportAgent
    ReportAgent --> StructuredGen[🏗️ 结构化生成]

    %% 溯源链接
    StructuredGen --> TraceableContent[🔗 可溯源内容]
    RelevantEvidence --> SourceRefs[📍 源引用]
    SourceRefs --> TraceableContent

    %% 验证和输出
    TraceableContent --> Validation[✅ 内容验证]
    Validation --> FinalReport[📋 最终报告]

    %% 可视化层
    FinalReport --> PDFViewer[📱 PDF 前端]
    SourceRefs --> PDFViewer
    BBoxInfo --> PDFViewer
    PDFViewer --> HighlightBoxes[🎯 高亮定位框]

    %% 样式定义
    classDef input fill:#e3f2fd
    classDef process fill:#f3e5f5
    classDef storage fill:#e8f5e8
    classDef agent fill:#fce4ec
    classDef output fill:#fff3e0
    classDef visual fill:#e0f2f1

    class PDF,UserQuery,JSONSchema input
    class MinerU,QueryAnalysis,EvidenceRetrieval,StructuredGen,Validation process
    class StructData,TextBlocks,BBoxInfo,PageInfo,EvidencePool,RelevantEvidence storage
    class ReportAgent agent
    class TraceableContent,SourceRefs,FinalReport output
    class PDFViewer,HighlightBoxes visual
```

### 核心特性

- **📄 PDF 处理**: 使用 MinerU 进行文档解析和结构化
- **🔍 证据池**: 基于坐标的精确文本定位
- **🔗 溯源链接**: 每个声明都可追溯到源文档位置
- **📊 结构化输出**: 基于 JSON Schema 的标准化报告格式

## 🌐 前端架构

### Dev UI (Angular)
- **技术栈**: Angular + Google ADK UI Components
- **功能**: Agent 开发和调试界面
- **特性**: 实时对话、会话管理、调试工具

### PDF Frontend (React)
- **技术栈**: React + PDF.js + Tailwind CSS
- **功能**: PDF 文档可视化和溯源定位
- **特性**: 
  - PDF 渲染和缩放
  - 文本块高亮定位
  - 证据溯源可视化
  - 搜索和导航

## 📈 监控和可观测性

### Langfuse 集成
- **功能**: LLM 调用追踪和分析
- **配置**: 通过环境变量自动配置
- **特性**: 成本分析、性能监控、对话追踪

### OpenTelemetry
- **功能**: 分布式追踪
- **范围**: API 调用、Agent 执行、工具调用
- **输出**: Langfuse + 内存导出器

## 🚀 部署架构

### 本地开发
```bash
# 环境准备
poetry install
poetry run uvicorn mareo.app.server:app --host 0.0.0.0 --port 3000 --reload
```

### Docker 部署
```bash
# 构建镜像
docker build -f containers/mareo/Dockerfile . -t mareo:latest

# 运行容器
docker run -p 3000:3000 -e LANGFUSE_PUBLIC_KEY=xxx mareo:latest
```

### Kubernetes 部署
- **CI/CD**: GitHub Actions 自动化流水线
- **环境**: 支持 PR 预览环境和生产环境
- **配置**: 通过 ConfigMap 和 Secret 管理环境变量

## 🔐 安全和认证

### 环境变量管理
- **开发环境**: `.env` 文件
- **生产环境**: Kubernetes Secrets
- **CI/CD**: GitHub Secrets

### API 安全
- **认证**: 基于会话的用户认证
- **授权**: 基于用户ID的资源访问控制
- **追踪**: 所有操作都有完整的审计日志

## 📚 开发指南

### 添加新 Agent
1. 在 `mareo/agent_hub/` 创建新目录
2. 实现 `create_xxx_agent()` 工厂函数
3. 配置模型和工具
4. 添加到 Agent Hub 注册

### 集成新 MCP 工具
1. 在 `mareo/common/tools/mcp/` 添加工具定义
2. 实现连接参数和认证
3. 在相关 Agent 中引用工具

### 前端扩展
- **Dev UI**: 修改 Angular 组件
- **PDF Frontend**: 扩展 React 组件功能

## 🔄 持续集成

### GitHub Actions 流水线
1. **代码检查**: Ruff linting
2. **测试**: Pytest 单元测试
3. **构建**: Docker 镜像构建
4. **部署**: Kubernetes 自动部署
5. **清理**: PR 关闭后资源清理

### 分支策略
- `main`: 生产分支
- `feat/*`: 功能分支
- `fix/*`: 修复分支
- PR 驱动的开发流程

---

*本文档基于项目当前状态生成，随着项目发展会持续更新。*
