line-length = 120
[lint]
select = [
    "E",
    "W",
    "F",
    "I",
    "Q",
    "B",
    "ASYNC",
    "UP006", # Use `list` instead of `List` for annotations
    "UP007", # Use `X | Y` instead of `Union[X, Y]`
    "UP008", # Use `X | None` instead of `Optional[X]`
]

ignore = [
    "E501",
    "B003",
    "B007",
    "B009",
    "B010",
    "B904",
    "B018",
    # Temporarily ignore ASYNC rules until they can be properly fixed in a separate PR
    "ASYNC110",
    "ASYNC220",
    "ASYNC221",
    "ASYNC230",
    "ASYNC251",
]

[lint.flake8-quotes]
docstring-quotes = "double"
inline-quotes = "single"

[format]
quote-style = "single"

[lint.flake8-bugbear]
extend-immutable-calls = ["Depends", "fastapi.Depends", "fastapi.params.Depends"]
