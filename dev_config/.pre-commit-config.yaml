repos:
  - repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.12.5
    hooks:
      # Run the linter.
      - id: ruff
        types_or: [ python, pyi ]
        args: [ --fix, --unsafe-fixes ]
        entry: ruff check --config dev_config/ruff.toml
      # Run the formatter.
      - id: ruff-format
        types_or: [ python, pyi ]
        entry: ruff format --config dev_config/ruff.toml
