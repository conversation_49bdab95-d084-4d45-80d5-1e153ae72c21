apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    alb.ingress.kubernetes.io/connection-drain-enabled: "true"
    alb.ingress.kubernetes.io/connection-drain-timeout: "300"
    alb.ingress.kubernetes.io/listen-ports: "[{\"HTTPS\": 443}]"
    alb.ingress.kubernetes.io/slow-start-duration: "30"
    alb.ingress.kubernetes.io/slow-start-enabled: "true"
    freelens.app/resource-version: v1
  name: mareo-ing
spec:
  ingressClassName: alb
  rules:
    - host: next.mareo.ai
      http:
        paths:
          - backend:
              service:
                name: mareo-svc
                port:
                  number: 3000
            path: /
            pathType: Prefix
