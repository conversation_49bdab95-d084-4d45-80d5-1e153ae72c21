apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    freelens.app/resource-version: v1
  labels:
    app: mareo
  name: mareo
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: mareo
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        alibabacloud.com/compute-class: general-purpose
        alibabacloud.com/compute-qos: default
        aliyun.com/app-language: python
        app: mareo
    spec:
      imagePullSecrets:
        - name: cr
      containers:
        - name: mareo
          image: ''
          imagePullPolicy: IfNotPresent
          resources:
            requests:
              cpu: 500m
              memory: 1Gi
          env:
            - name: aliyun_logs_mareo-next
              value: stdout
          envFrom:
            - configMapRef:
                name: mareo-var
            - secretRef:
                name: mareo-secret
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /?check=liveness
              port: 3000
              scheme: HTTP
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /?check=readiness
              port: 3000
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 3
            timeoutSeconds: 5
          startupProbe:
            failureThreshold: 30
            httpGet:
              path: /?check=startup
              port: 3000
              scheme: HTTP
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: { }
      terminationGracePeriodSeconds: 30
