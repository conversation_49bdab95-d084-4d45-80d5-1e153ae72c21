from unittest.mock import Mock

import pytest
from google.adk.agents.callback_context import CallbackContext
from google.adk.models.llm_response import LlmResponse
from google.genai import types

from mareo.common.plugins.remove_empty_tool_call_plugin import RemoveEmptyToolCallPlugin


class TestRemoveEmptyToolCallPlugin:
    """Test suite for RemoveEmptyToolCallPlugin"""

    def setup_method(self):
        """Set up test fixtures"""
        self.plugin = RemoveEmptyToolCallPlugin(name='test_plugin')
        self.callback_context = Mock(spec=CallbackContext)

    @pytest.mark.asyncio
    async def test_after_model_callback_no_content(self):
        """Test when LlmResponse has no content"""
        llm_response = LlmResponse(content=None, grounding_metadata=None)

        result = await self.plugin.after_model_callback(
            callback_context=self.callback_context, llm_response=llm_response
        )

        assert result is None

    @pytest.mark.asyncio
    async def test_after_model_callback_empty_parts(self):
        """Test when LlmResponse has content but no parts"""
        content = types.Content(role='assistant', parts=[])
        llm_response = LlmResponse(content=content, grounding_metadata=None)

        result = await self.plugin.after_model_callback(
            callback_context=self.callback_context, llm_response=llm_response
        )

        assert result is None

    @pytest.mark.asyncio
    async def test_after_model_callback_no_function_calls(self):
        """Test when response has only text parts, no function calls"""
        text_part = types.Part.from_text(text='This is a text response')
        content = types.Content(role='assistant', parts=[text_part])
        llm_response = LlmResponse(content=content, grounding_metadata=None)

        result = await self.plugin.after_model_callback(
            callback_context=self.callback_context, llm_response=llm_response
        )

        assert result is None

    @pytest.mark.asyncio
    async def test_after_model_callback_empty_function_call(self):
        """Test when response has empty function call (both name and args empty)"""
        # Create empty function call
        empty_function_call = types.FunctionCall(name='', args={})
        function_part = types.Part(function_call=empty_function_call)

        content = types.Content(role='assistant', parts=[function_part])
        llm_response = LlmResponse(content=content, grounding_metadata=None)

        result = await self.plugin.after_model_callback(
            callback_context=self.callback_context, llm_response=llm_response
        )

        # Should return modified response with empty content since all parts were removed
        assert result is not None
        assert result.content is None
        assert result.grounding_metadata == llm_response.grounding_metadata

    @pytest.mark.asyncio
    async def test_after_model_callback_valid_function_call(self):
        """Test when response has valid function call"""
        valid_function_call = types.FunctionCall(name='get_weather', args={'location': 'New York'})
        function_part = types.Part(function_call=valid_function_call)

        content = types.Content(role='assistant', parts=[function_part])
        llm_response = LlmResponse(content=content, grounding_metadata=None)

        result = await self.plugin.after_model_callback(
            callback_context=self.callback_context, llm_response=llm_response
        )

        # Should return None since no modification is needed
        assert result is None

    @pytest.mark.asyncio
    async def test_after_model_callback_function_call_empty_name_only(self):
        """Test when function call has empty name but non-empty args"""
        function_call = types.FunctionCall(name='', args={'key': 'value'})
        function_part = types.Part(function_call=function_call)

        content = types.Content(role='assistant', parts=[function_part])
        llm_response = LlmResponse(content=content, grounding_metadata=None)

        result = await self.plugin.after_model_callback(
            callback_context=self.callback_context, llm_response=llm_response
        )

        # Should return None since name is empty but args is not
        assert result is None

    @pytest.mark.asyncio
    async def test_after_model_callback_function_call_empty_args_only(self):
        """Test when function call has non-empty name but empty args"""
        function_call = types.FunctionCall(name='test_function', args={})
        function_part = types.Part(function_call=function_call)

        content = types.Content(role='assistant', parts=[function_part])
        llm_response = LlmResponse(content=content, grounding_metadata=None)

        result = await self.plugin.after_model_callback(
            callback_context=self.callback_context, llm_response=llm_response
        )

        # Should return None since args is empty but name is not
        assert result is None

    @pytest.mark.asyncio
    async def test_after_model_callback_mixed_parts(self):
        """Test when response has mix of text, empty function calls, and valid function calls"""
        text_part = types.Part.from_text(text='Here is the result:')

        empty_function_call = types.FunctionCall(name='', args={})
        empty_function_part = types.Part(function_call=empty_function_call)

        valid_function_call = types.FunctionCall(name='get_weather', args={'location': 'Tokyo'})
        valid_function_part = types.Part(function_call=valid_function_call)

        content = types.Content(role='assistant', parts=[text_part, empty_function_part, valid_function_part])
        llm_response = LlmResponse(content=content, grounding_metadata=None)

        result = await self.plugin.after_model_callback(
            callback_context=self.callback_context, llm_response=llm_response
        )

        # Should return modified response with empty function call removed
        assert result is not None
        assert result.content is not None
        assert len(result.content.parts) == 2
        assert result.content.parts[0].text == 'Here is the result:'
        assert result.content.parts[1].function_call.name == 'get_weather'
        assert result.grounding_metadata == llm_response.grounding_metadata

    @pytest.mark.asyncio
    async def test_after_model_callback_multiple_empty_function_calls(self):
        """Test when response has multiple empty function calls"""
        empty_function_call_1 = types.FunctionCall(name='', args={})
        empty_function_part_1 = types.Part(function_call=empty_function_call_1)

        empty_function_call_2 = types.FunctionCall(name='', args={})
        empty_function_part_2 = types.Part(function_call=empty_function_call_2)

        text_part = types.Part.from_text(text='Response text')

        content = types.Content(role='assistant', parts=[empty_function_part_1, text_part, empty_function_part_2])
        llm_response = LlmResponse(content=content, grounding_metadata=None)

        result = await self.plugin.after_model_callback(
            callback_context=self.callback_context, llm_response=llm_response
        )

        # Should return modified response with both empty function calls removed
        assert result is not None
        assert result.content is not None
        assert len(result.content.parts) == 1
        assert result.content.parts[0].text == 'Response text'

    @pytest.mark.asyncio
    async def test_after_model_callback_preserves_role(self):
        """Test that the modified response preserves the original role"""
        empty_function_call = types.FunctionCall(name='', args={})
        empty_function_part = types.Part(function_call=empty_function_call)

        text_part = types.Part.from_text(text='Some text')

        content = types.Content(role='user', parts=[empty_function_part, text_part])
        llm_response = LlmResponse(content=content, grounding_metadata=None)

        result = await self.plugin.after_model_callback(
            callback_context=self.callback_context, llm_response=llm_response
        )

        assert result is not None
        assert result.content.role == 'user'

    @pytest.mark.asyncio
    async def test_after_model_callback_function_call_none_name(self):
        """Test when function call has None name"""
        function_call = types.FunctionCall(name=None, args={})
        function_part = types.Part(function_call=function_call)

        content = types.Content(role='assistant', parts=[function_part])
        llm_response = LlmResponse(content=content, grounding_metadata=None)

        result = await self.plugin.after_model_callback(
            callback_context=self.callback_context, llm_response=llm_response
        )

        # Should return modified response since None name is considered empty
        assert result is not None
        assert result.content is None

    @pytest.mark.asyncio
    async def test_after_model_callback_function_call_none_args(self):
        """Test when function call has None args"""
        function_call = types.FunctionCall(name='', args=None)
        function_part = types.Part(function_call=function_call)

        content = types.Content(role='assistant', parts=[function_part])
        llm_response = LlmResponse(content=content, grounding_metadata=None)

        result = await self.plugin.after_model_callback(
            callback_context=self.callback_context, llm_response=llm_response
        )

        # Should return modified response since None args is considered empty
        assert result is not None
        assert result.content is None
