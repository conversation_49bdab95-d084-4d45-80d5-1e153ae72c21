# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import asyncio
import time
from typing import Awaitable, Callable
from unittest.mock import Mock

import pytest
from google.adk.agents.readonly_context import ReadonlyContext

from mareo.common.prompts.base import PromptProvider


class MockPromptProvider(PromptProvider[str]):
    """Mock implementation of PromptProvider for testing."""

    def __init__(self, cache_ttl: int = 60, fallback_prompts: dict[str, str] = None):
        super().__init__(cache_ttl=cache_ttl, fallback_prompts=fallback_prompts)
        self.fetch_call_count = 0
        self.fetch_responses = {}
        self.should_fail = False

    async def _fetch_prompt(self, *args) -> str:
        """Mock fetch implementation."""
        self.fetch_call_count += 1
        if self.should_fail:
            raise Exception('Mock fetch failure')
        key = tuple(str(arg) for arg in args)
        return self.fetch_responses.get(key, f'prompt_for_{key}')

    def _generate_fallback_keys(self, *args) -> list[str]:
        """Generate fallback keys for testing."""
        keys = []
        if len(args) == 1:
            keys.append(f'mock_{args[0]}')
            keys.append(str(args[0]))
        elif len(args) == 2:
            keys.append(f'{args[0]}_{args[1]}')
            keys.append(str(args[0]))
        return keys

    def get_provider(self, *args) -> Callable[[ReadonlyContext], Awaitable[str]]:
        """Mock provider function."""

        async def provider_func(context: ReadonlyContext) -> str:
            result = await self._get_prompt(context, *args)
            if isinstance(result, str):
                return result
            return str(result)

        return provider_func


@pytest.fixture
def mock_provider():
    """Create a mock prompt provider for testing."""
    return MockPromptProvider(cache_ttl=1)  # Short TTL for testing


@pytest.fixture
def mock_context():
    """Create a mock readonly context."""
    context = Mock(spec=ReadonlyContext)
    context.state = {}
    return context


@pytest.mark.asyncio
async def test_prompt_provider_basic_functionality(mock_provider):
    """Test basic prompt fetching functionality."""
    result = await mock_provider._get_prompt(None, 'test_prompt')
    assert result == "prompt_for_('test_prompt',)"
    assert mock_provider.fetch_call_count == 1


@pytest.mark.asyncio
async def test_prompt_provider_caching(mock_provider):
    """Test that prompts are cached properly."""
    # First call should fetch from provider
    result1 = await mock_provider._get_prompt(None, 'test_prompt')
    assert result1 == "prompt_for_('test_prompt',)"
    assert mock_provider.fetch_call_count == 1

    # Second call should return from cache
    result2 = await mock_provider._get_prompt(None, 'test_prompt')
    assert result2 == result1
    assert mock_provider.fetch_call_count == 1  # Should not increment


@pytest.mark.asyncio
async def test_prompt_provider_cache_ttl_expiry(mock_provider):
    """Test that cache entries expire after TTL."""
    # First call
    result1 = await mock_provider._get_prompt(None, 'test_prompt')
    assert mock_provider.fetch_call_count == 1

    # Wait for cache to expire
    await asyncio.sleep(1.1)

    # Second call should fetch again due to expiry
    result2 = await mock_provider._get_prompt(None, 'test_prompt')
    assert result2 == result1
    assert mock_provider.fetch_call_count == 2


@pytest.mark.asyncio
async def test_prompt_provider_system_prompt_from_state(mock_provider, mock_context):
    """Test that system_prompt from context.state takes precedence."""
    mock_context.state['system_prompt'] = 'system_prompt_from_state'

    result = await mock_provider._get_prompt(mock_context, 'test_prompt')
    assert result == 'system_prompt_from_state'
    # Should not call fetch_prompt when system_prompt is in state
    assert mock_provider.fetch_call_count == 0


@pytest.mark.asyncio
async def test_prompt_provider_no_system_prompt_fallback(mock_provider, mock_context):
    """Test fallback to fetch_prompt when no system_prompt in state."""
    # No system_prompt in state
    result = await mock_provider._get_prompt(mock_context, 'test_prompt')
    assert result == "prompt_for_('test_prompt',)"
    assert mock_provider.fetch_call_count == 1


@pytest.mark.asyncio
async def test_prompt_provider_none_system_prompt(mock_provider, mock_context):
    """Test that None system_prompt falls back to fetch_prompt."""
    mock_context.state['system_prompt'] = None

    result = await mock_provider._get_prompt(mock_context, 'test_prompt')
    assert result == "prompt_for_('test_prompt',)"
    assert mock_provider.fetch_call_count == 1


@pytest.mark.asyncio
async def test_prompt_provider_empty_string_system_prompt(mock_provider, mock_context):
    """Test that empty string system_prompt falls back to fetch_prompt."""
    mock_context.state['system_prompt'] = ''

    result = await mock_provider._get_prompt(mock_context, 'test_prompt')
    assert result == "prompt_for_('test_prompt',)"
    assert mock_provider.fetch_call_count == 1


def test_cache_key_generation(mock_provider):
    """Test cache key generation from arguments."""
    key1 = mock_provider._get_cache_key('prompt1', 'label1')
    key2 = mock_provider._get_cache_key('prompt1', 'label1')
    key3 = mock_provider._get_cache_key('prompt2', 'label1')

    assert key1 == key2
    assert key1 != key3
    assert isinstance(key1, tuple)
    assert key1 == ('prompt1', 'label1')


@pytest.mark.asyncio
async def test_clear_cache(mock_provider):
    """Test cache clearing functionality."""
    # Add something to cache by calling _get_prompt
    await mock_provider._get_prompt(None, 'test')
    assert len(mock_provider._cache) == 1

    await mock_provider.clear_cache()
    assert len(mock_provider._cache) == 0


@pytest.mark.asyncio
async def test_cache_stats(mock_provider):
    """Test cache statistics functionality."""
    # Initially empty
    stats = await mock_provider.get_cache_stats()
    assert stats['total_entries'] == 0
    assert stats['valid_entries'] == 0
    assert stats['expired_entries_found'] == 0
    assert stats['cleaned_entries'] == 0
    assert stats['cache_ttl'] == 1

    # Add some entries
    await mock_provider._get_prompt(None, 'prompt1')
    await mock_provider._get_prompt(None, 'prompt2')

    stats = await mock_provider.get_cache_stats()
    assert stats['total_entries'] == 2
    assert stats['valid_entries'] == 2


@pytest.mark.asyncio
async def test_cache_stats_no_cleanup(mock_provider):
    """Test cache statistics without cleanup."""
    # Add some entries and let them expire
    await mock_provider._get_prompt(None, 'prompt1')
    await asyncio.sleep(1.1)  # Wait for expiry

    # Get stats without cleanup
    stats = await mock_provider.get_cache_stats(cleanup_expired=False)
    assert stats['total_entries'] == 1  # Entry still in cache
    assert stats['expired_entries_found'] == 1  # But marked as expired
    assert stats['cleaned_entries'] == 0  # No cleanup performed


@pytest.mark.asyncio
async def test_cleanup_expired_cache(mock_provider):
    """Test manual cleanup of expired cache entries."""
    # Add entries
    await mock_provider._get_prompt(None, 'prompt1')
    await mock_provider._get_prompt(None, 'prompt2')
    assert len(mock_provider._cache) == 2

    # Wait for expiry
    await asyncio.sleep(1.1)

    # Cleanup expired entries
    cleaned_count = await mock_provider.cleanup_expired_cache()
    assert cleaned_count == 2
    assert len(mock_provider._cache) == 0


@pytest.mark.asyncio
async def test_concurrent_cache_access(mock_provider):
    """Test that concurrent access to cache is thread-safe."""

    async def fetch_task(prompt_name):
        return await mock_provider._get_prompt(None, prompt_name)

    # Create multiple concurrent tasks
    tasks = [fetch_task(f'prompt_{i}') for i in range(10)]
    results = await asyncio.gather(*tasks)

    # All tasks should complete successfully
    assert len(results) == 10
    # Cache should contain 10 entries
    assert len(mock_provider._cache) == 10


@pytest.mark.asyncio
async def test_get_provider_integration(mock_provider, mock_context):
    """Test integration with get_provider method."""
    provider_func = mock_provider.get_provider('test_prompt', 'test_label')

    # Test with system_prompt in state
    mock_context.state['system_prompt'] = 'system_from_state'
    result = await provider_func(mock_context)
    assert result == 'system_from_state'

    # Test without system_prompt in state
    mock_context.state.clear()
    result = await provider_func(mock_context)
    assert result == "prompt_for_('test_prompt', 'test_label')"


@pytest.mark.asyncio
async def test_cache_entry_creation(mock_provider):
    """Test that cache entries are created with proper timestamps."""
    start_time = time.time()

    # Add real entry
    await mock_provider._get_prompt(None, 'test')
    end_time = time.time()

    # Check timestamp is reasonable
    cache_entry = mock_provider._cache[('test',)]
    assert start_time <= cache_entry.timestamp <= end_time


@pytest.mark.asyncio
async def test_different_args_different_cache_entries(mock_provider):
    """Test that different arguments create different cache entries."""
    await mock_provider._get_prompt(None, 'prompt1')
    await mock_provider._get_prompt(None, 'prompt2')
    await mock_provider._get_prompt(None, 'prompt1', 'label1')

    assert len(mock_provider._cache) == 3
    assert ('prompt1',) in mock_provider._cache
    assert ('prompt2',) in mock_provider._cache
    assert ('prompt1', 'label1') in mock_provider._cache


# === Fallback Mechanism Tests ===


@pytest.mark.asyncio
async def test_add_fallback_prompt():
    """Test adding fallback prompts."""
    provider = MockPromptProvider()

    # Add fallback prompts
    provider.add_fallback_prompt('test_key', 'test fallback content')
    provider.add_fallback_prompt('another_key', 'another fallback content')

    # Verify they were added
    assert provider._get_fallback_prompt('test_key') == 'test fallback content'
    assert provider._get_fallback_prompt('another_key') == 'another fallback content'
    assert provider._get_fallback_prompt('nonexistent') is None


@pytest.mark.asyncio
async def test_fallback_prompts_from_constructor():
    """Test fallback prompts passed via constructor."""
    fallback_prompts = {'key1': 'content1', 'key2': 'content2'}
    provider = MockPromptProvider(fallback_prompts=fallback_prompts)

    assert provider._get_fallback_prompt('key1') == 'content1'
    assert provider._get_fallback_prompt('key2') == 'content2'


@pytest.mark.asyncio
async def test_fallback_prompts_from_environment():
    """Test loading fallback prompts from environment variables."""
    import os

    # Set environment variables
    os.environ['FALLBACK_PROMPT_TEST_ENV'] = 'env test content'
    os.environ['FALLBACK_PROMPT_ANOTHER_TEST'] = 'another env content'

    try:
        provider = MockPromptProvider()

        # Should load with normalized names (underscore to dash, lowercase)
        assert provider._get_fallback_prompt('test-env') == 'env test content'
        assert provider._get_fallback_prompt('another-test') == 'another env content'

    finally:
        # Clean up environment variables
        del os.environ['FALLBACK_PROMPT_TEST_ENV']
        del os.environ['FALLBACK_PROMPT_ANOTHER_TEST']


@pytest.mark.asyncio
async def test_generate_fallback_keys():
    """Test the key generation strategy."""
    provider = MockPromptProvider()

    # Test single argument
    keys = provider._generate_fallback_keys('test')
    assert keys == ['mock_test', 'test']

    # Test two arguments
    keys = provider._generate_fallback_keys('prompt', 'label')
    assert keys == ['prompt_label', 'prompt']

    # Test empty arguments
    keys = provider._generate_fallback_keys()
    assert keys == []


@pytest.mark.asyncio
async def test_get_fallback_for_args():
    """Test getting fallback prompt for arguments."""
    fallback_prompts = {
        'mock_test': 'specific mock fallback',
        'test': 'general test fallback',
        'prompt_label': 'labeled prompt fallback',
        'prompt': 'general prompt fallback',
        'default': 'default fallback',
    }
    provider = MockPromptProvider(fallback_prompts=fallback_prompts)

    # Test single argument - should use most specific first
    result = provider._get_fallback_for_args('test')
    assert result == 'specific mock fallback'

    # Test two arguments - should use most specific first
    result = provider._get_fallback_for_args('prompt', 'label')
    assert result == 'labeled prompt fallback'

    # Test fallback to less specific
    fallback_prompts_partial = {'test': 'general test fallback', 'default': 'default fallback'}
    provider_partial = MockPromptProvider(fallback_prompts=fallback_prompts_partial)
    result = provider_partial._get_fallback_for_args('test')
    assert result == 'general test fallback'

    # Test fallback to default
    result = provider_partial._get_fallback_for_args('unknown')
    assert result == 'default fallback'

    # Test no fallback available
    provider_empty = MockPromptProvider()
    result = provider_empty._get_fallback_for_args('unknown')
    assert result is None


@pytest.mark.asyncio
async def test_fallback_mechanism_with_provider_failure():
    """Test fallback mechanism when provider fails."""
    fallback_prompts = {'mock_test': 'fallback for test', 'default': 'default fallback'}
    provider = MockPromptProvider(fallback_prompts=fallback_prompts)
    provider.should_fail = True

    # Should use fallback when provider fails
    result = await provider._get_prompt(None, 'test')
    assert result == 'fallback for test'
    assert provider.fetch_call_count == 1  # Still called fetch, but failed

    # Should use default fallback for unknown prompt
    result = await provider._get_prompt(None, 'unknown')
    assert result == 'default fallback'


@pytest.mark.asyncio
async def test_fallback_mechanism_failure_without_fallback():
    """Test that provider fails properly when no fallback is available."""
    provider = MockPromptProvider()  # No fallback prompts
    provider.should_fail = True

    with pytest.raises(Exception, match='Mock fetch failure'):
        await provider._get_prompt(None, 'test')


@pytest.mark.asyncio
async def test_fallback_case_insensitive_matching():
    """Test case-insensitive fallback prompt matching."""
    fallback_prompts = {'Test-Key': 'test content', 'another_key': 'another content'}
    provider = MockPromptProvider(fallback_prompts=fallback_prompts)

    # Test case-insensitive matching
    assert provider._get_fallback_prompt('test-key') == 'test content'
    assert provider._get_fallback_prompt('TEST_KEY') == 'test content'
    assert provider._get_fallback_prompt('Another-Key') == 'another content'
    assert provider._get_fallback_prompt('ANOTHER_KEY') == 'another content'


@pytest.mark.asyncio
async def test_fallback_priority_over_cache():
    """Test that system_prompt from context has priority over fallback."""
    fallback_prompts = {'mock_test': 'fallback content', 'default': 'default fallback'}
    provider = MockPromptProvider(fallback_prompts=fallback_prompts)
    provider.should_fail = True

    # Create context with system_prompt
    context = Mock(spec=ReadonlyContext)
    context.state = {'system_prompt': 'system prompt from context'}

    # Should use system_prompt from context, not fallback
    result = await provider._get_prompt(context, 'test')
    assert result == 'system prompt from context'
    assert provider.fetch_call_count == 0  # Should not call fetch at all


@pytest.mark.asyncio
async def test_fallback_integration_with_provider_function():
    """Test fallback mechanism integration with provider functions."""
    fallback_prompts = {'mock_test': 'fallback for test', 'default': 'default fallback'}
    provider = MockPromptProvider(fallback_prompts=fallback_prompts)
    provider.should_fail = True

    # Get provider function
    provider_func = provider.get_provider('test')

    # Create mock context
    context = Mock(spec=ReadonlyContext)
    context.state = {}

    # Should use fallback through provider function
    result = await provider_func(context)
    assert result == 'fallback for test'
