# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import os
from unittest.mock import AsyncMock, Mock, patch

import pytest
from google.adk.agents.readonly_context import ReadonlyContext

from mareo.common.prompts.langfuse_provider import LangfusePrompt


@pytest.fixture
def mock_langfuse_client():
    """Create a mock Langfuse client."""
    client = Mock()
    client.async_api = Mock()
    client.async_api.prompts = Mock()
    return client


@pytest.fixture
def mock_text_prompt_client():
    """Create a mock TextPromptClient."""
    client = Mock()
    client.compile.return_value = 'compiled_prompt_text'
    return client


@pytest.fixture
def mock_prompt_response():
    """Create a mock prompt response from Langfuse API."""
    prompt = Mock()
    prompt.name = 'test_prompt'
    prompt.label = 'production'
    return prompt


@pytest.fixture
def mock_context():
    """Create a mock readonly context."""
    context = Mock(spec=ReadonlyContext)
    context.state = {}
    return context


@pytest.fixture
def langfuse_provider(mock_langfuse_client):
    """Create a LangfusePrompt instance with mocked client."""
    with patch('mareo.common.prompts.langfuse_provider.get_client', return_value=mock_langfuse_client):
        return LangfusePrompt(cache_ttl=60)


@pytest.mark.asyncio
async def test_langfuse_prompt_initialization():
    """Test LangfusePrompt initialization."""
    with patch('mareo.common.prompts.langfuse_provider.get_client') as mock_get_client:
        mock_client = Mock()
        mock_get_client.return_value = mock_client

        provider = LangfusePrompt(cache_ttl=120)
        assert provider._cache_ttl == 120
        assert provider._langfuse == mock_client
        mock_get_client.assert_called_once()


@pytest.mark.asyncio
async def test_langfuse_prompt_initialization_with_env_ttl():
    """Test LangfusePrompt initialization with environment TTL."""
    with patch.dict(os.environ, {'LANGFUSE_CACHE_TTL': '300'}):
        with patch('mareo.common.prompts.langfuse_provider.get_client') as mock_get_client:
            mock_client = Mock()
            mock_get_client.return_value = mock_client

            provider = LangfusePrompt()
            assert provider._cache_ttl == 300


@pytest.mark.asyncio
async def test_fetch_prompt(langfuse_provider, mock_prompt_response, mock_text_prompt_client):
    """Test _fetch_prompt method."""
    with patch('mareo.common.prompts.langfuse_provider.TextPromptClient', return_value=mock_text_prompt_client):
        langfuse_provider._langfuse.async_api.prompts.get = AsyncMock(return_value=mock_prompt_response)

        result = await langfuse_provider._fetch_prompt('test_prompt', 'production')

        assert result == mock_text_prompt_client
        langfuse_provider._langfuse.async_api.prompts.get.assert_called_once_with('test_prompt', label='production')


@pytest.mark.asyncio
async def test_get_provider_with_system_prompt_in_state(langfuse_provider, mock_context):
    """Test get_provider when system_prompt is in context.state."""
    mock_context.state['system_prompt'] = 'system_prompt_from_state'

    provider_func = langfuse_provider.get_provider('test_prompt', 'production')
    result = await provider_func(mock_context)

    assert result == 'system_prompt_from_state'


@pytest.mark.asyncio
async def test_get_provider_with_langfuse_prompt(
    langfuse_provider, mock_context, mock_prompt_response, mock_text_prompt_client
):
    """Test get_provider when fetching from Langfuse and compiling."""
    # No system_prompt in state
    mock_context.state = {}

    with patch('mareo.common.prompts.langfuse_provider.TextPromptClient', return_value=mock_text_prompt_client):
        langfuse_provider._langfuse.async_api.prompts.get = AsyncMock(return_value=mock_prompt_response)

        provider_func = langfuse_provider.get_provider('test_prompt', 'production')
        result = await provider_func(mock_context)

        assert result == 'compiled_prompt_text'
        mock_text_prompt_client.compile.assert_called_once()


@pytest.mark.asyncio
async def test_get_provider_string_result_direct_return(langfuse_provider, mock_context):
    """Test get_provider when _get_prompt returns a string (from state)."""
    mock_context.state['system_prompt'] = 'direct_string_prompt'

    provider_func = langfuse_provider.get_provider('test_prompt', 'production')
    result = await provider_func(mock_context)

    # Should return the string directly without calling compile
    assert result == 'direct_string_prompt'


@pytest.mark.asyncio
async def test_get_provider_text_prompt_client_compile(
    langfuse_provider, mock_context, mock_prompt_response, mock_text_prompt_client
):
    """Test get_provider when _get_prompt returns TextPromptClient."""
    mock_context.state = {}  # No system_prompt

    with patch('mareo.common.prompts.langfuse_provider.TextPromptClient', return_value=mock_text_prompt_client):
        langfuse_provider._langfuse.async_api.prompts.get = AsyncMock(return_value=mock_prompt_response)

        provider_func = langfuse_provider.get_provider('test_prompt', 'production')
        result = await provider_func(mock_context)

        # Should call compile on the TextPromptClient
        assert result == 'compiled_prompt_text'
        mock_text_prompt_client.compile.assert_called_once()


def test_get_provider_default_label_from_env(langfuse_provider):
    """Test get_provider uses environment variable for default label."""
    with patch.dict(os.environ, {'LANGFUSE_PROMPT_LABEL': 'staging'}):
        provider_func = langfuse_provider.get_provider('test_prompt')

        # We can't easily test the label without calling the function,
        # but we can verify the function was created successfully
        assert callable(provider_func)


def test_get_provider_default_label_production(langfuse_provider):
    """Test get_provider defaults to 'production' label."""
    with patch.dict(os.environ, {}, clear=True):
        # Remove any existing LANGFUSE_PROMPT_LABEL
        if 'LANGFUSE_PROMPT_LABEL' in os.environ:
            del os.environ['LANGFUSE_PROMPT_LABEL']

        provider_func = langfuse_provider.get_provider('test_prompt')
        assert callable(provider_func)


@pytest.mark.asyncio
async def test_caching_behavior(langfuse_provider, mock_context, mock_prompt_response, mock_text_prompt_client):
    """Test that prompt results are cached properly."""
    mock_context.state = {}

    with patch('mareo.common.prompts.langfuse_provider.TextPromptClient', return_value=mock_text_prompt_client):
        langfuse_provider._langfuse.async_api.prompts.get = AsyncMock(return_value=mock_prompt_response)

        provider_func = langfuse_provider.get_provider('test_prompt', 'production')

        # First call
        result1 = await provider_func(mock_context)
        assert result1 == 'compiled_prompt_text'

        # Second call should use cache (API should only be called once)
        result2 = await provider_func(mock_context)
        assert result2 == 'compiled_prompt_text'

        # Verify API was only called once due to caching
        assert langfuse_provider._langfuse.async_api.prompts.get.call_count == 1


@pytest.mark.asyncio
async def test_system_prompt_bypasses_cache(langfuse_provider, mock_context, mock_prompt_response):
    """Test that system_prompt from state bypasses cache and API calls."""
    mock_context.state['system_prompt'] = 'cached_system_prompt'

    langfuse_provider._langfuse.async_api.prompts.get = AsyncMock(return_value=mock_prompt_response)

    provider_func = langfuse_provider.get_provider('test_prompt', 'production')
    result = await provider_func(mock_context)

    assert result == 'cached_system_prompt'
    # Should not call Langfuse API when system_prompt is in state
    langfuse_provider._langfuse.async_api.prompts.get.assert_not_called()


@pytest.mark.asyncio
async def test_different_prompt_names_different_cache_entries(
    langfuse_provider, mock_context, mock_prompt_response, mock_text_prompt_client
):
    """Test that different prompt names create different cache entries."""
    mock_context.state = {}

    with patch('mareo.common.prompts.langfuse_provider.TextPromptClient', return_value=mock_text_prompt_client):
        langfuse_provider._langfuse.async_api.prompts.get = AsyncMock(return_value=mock_prompt_response)

        provider_func1 = langfuse_provider.get_provider('prompt1', 'production')
        provider_func2 = langfuse_provider.get_provider('prompt2', 'production')

        await provider_func1(mock_context)
        await provider_func2(mock_context)

        # Should call API twice for different prompts
        assert langfuse_provider._langfuse.async_api.prompts.get.call_count == 2


@pytest.mark.asyncio
async def test_different_labels_different_cache_entries(
    langfuse_provider, mock_context, mock_prompt_response, mock_text_prompt_client
):
    """Test that different labels create different cache entries."""
    mock_context.state = {}

    with patch('mareo.common.prompts.langfuse_provider.TextPromptClient', return_value=mock_text_prompt_client):
        langfuse_provider._langfuse.async_api.prompts.get = AsyncMock(return_value=mock_prompt_response)

        provider_func1 = langfuse_provider.get_provider('test_prompt', 'production')
        provider_func2 = langfuse_provider.get_provider('test_prompt', 'staging')

        await provider_func1(mock_context)
        await provider_func2(mock_context)

        # Should call API twice for different labels
        assert langfuse_provider._langfuse.async_api.prompts.get.call_count == 2


@pytest.mark.asyncio
async def test_error_handling_in_fetch_prompt(langfuse_provider):
    """Test error handling in _fetch_prompt method."""
    langfuse_provider._langfuse.async_api.prompts.get = AsyncMock(side_effect=Exception('API Error'))

    with pytest.raises(Exception, match='API Error'):
        await langfuse_provider._fetch_prompt('test_prompt', 'production')


@pytest.mark.asyncio
async def test_provider_function_signature(langfuse_provider):
    """Test that provider function has correct signature."""
    provider_func = langfuse_provider.get_provider('test_prompt', 'production')

    # Should be callable
    assert callable(provider_func)

    # Should accept ReadonlyContext and return str
    mock_context = Mock(spec=ReadonlyContext)
    mock_context.state = {'system_prompt': 'test'}

    result = await provider_func(mock_context)
    assert isinstance(result, str)


def test_langfuse_prompt_singleton():
    """Test that langfuse_prompt module variable is properly created."""
    from mareo.common.prompts.langfuse_provider import langfuse_prompt

    assert isinstance(langfuse_prompt, LangfusePrompt)


@pytest.mark.asyncio
async def test_none_system_prompt_falls_back_to_langfuse(
    langfuse_provider, mock_context, mock_prompt_response, mock_text_prompt_client
):
    """Test that None system_prompt falls back to Langfuse fetch."""
    mock_context.state['system_prompt'] = None

    with patch('mareo.common.prompts.langfuse_provider.TextPromptClient', return_value=mock_text_prompt_client):
        langfuse_provider._langfuse.async_api.prompts.get = AsyncMock(return_value=mock_prompt_response)

        provider_func = langfuse_provider.get_provider('test_prompt', 'production')
        result = await provider_func(mock_context)

        assert result == 'compiled_prompt_text'
        langfuse_provider._langfuse.async_api.prompts.get.assert_called_once()


@pytest.mark.asyncio
async def test_empty_string_system_prompt_falls_back_to_langfuse(
    langfuse_provider, mock_context, mock_prompt_response, mock_text_prompt_client
):
    """Test that empty string system_prompt falls back to Langfuse fetch."""
    mock_context.state['system_prompt'] = ''

    with patch('mareo.common.prompts.langfuse_provider.TextPromptClient', return_value=mock_text_prompt_client):
        langfuse_provider._langfuse.async_api.prompts.get = AsyncMock(return_value=mock_prompt_response)

        provider_func = langfuse_provider.get_provider('test_prompt', 'production')
        result = await provider_func(mock_context)

        assert result == 'compiled_prompt_text'
        langfuse_provider._langfuse.async_api.prompts.get.assert_called_once()


# === Fallback Mechanism Tests for LangfusePrompt ===


@pytest.fixture
def langfuse_provider_with_fallbacks():
    """Create a LangfusePrompt with fallback prompts for testing."""
    fallback_prompts = {
        'chat_production': 'Production chat fallback',
        'chat': 'General chat fallback',
        'search_beta': 'Beta search fallback',
        'default': 'Default fallback prompt',
    }

    with patch('mareo.common.prompts.langfuse_provider.get_client') as mock_get_client:
        mock_client = Mock()
        mock_client.async_api = Mock()
        mock_client.async_api.prompts = Mock()
        mock_get_client.return_value = mock_client

        provider = LangfusePrompt(fallback_prompts=fallback_prompts)
        provider._langfuse = mock_client
        return provider


@pytest.mark.asyncio
async def test_langfuse_generate_fallback_keys():
    """Test Langfuse key generation strategy."""
    provider = LangfusePrompt()

    # Test with label
    keys = provider._generate_fallback_keys('chat', 'production')
    assert keys == ['chat_production', 'chat']

    # Test without label
    keys = provider._generate_fallback_keys('search', None)
    assert keys == ['search']

    # Test with empty string label (should be treated as None)
    keys = provider._generate_fallback_keys('test', '')
    assert keys == ['test']


@pytest.mark.asyncio
async def test_langfuse_add_fallback_prompt():
    """Test adding fallback prompts to LangfusePrompt."""
    provider = LangfusePrompt()

    # Add fallback without label
    provider.add_fallback_prompt('chat', 'Chat fallback content')
    assert provider._get_fallback_prompt('chat') == 'Chat fallback content'

    # Add fallback with label
    provider.add_fallback_prompt('chat', 'Production chat content', 'production')
    assert provider._get_fallback_prompt('chat_production') == 'Production chat content'

    # Verify both exist
    assert provider._get_fallback_prompt('chat') == 'Chat fallback content'
    assert provider._get_fallback_prompt('chat_production') == 'Production chat content'


@pytest.mark.asyncio
async def test_langfuse_fallback_mechanism_with_api_failure(langfuse_provider_with_fallbacks, mock_context):
    """Test fallback mechanism when Langfuse API fails."""
    # Mock API failure
    langfuse_provider_with_fallbacks._langfuse.async_api.prompts.get = AsyncMock(
        side_effect=Exception('Langfuse API unavailable')
    )

    # Should use specific fallback
    result = await langfuse_provider_with_fallbacks._get_prompt(mock_context, 'chat', 'production')
    assert result == 'Production chat fallback'

    # Should use general fallback when specific not available
    result = await langfuse_provider_with_fallbacks._get_prompt(mock_context, 'chat', 'staging')
    assert result == 'General chat fallback'

    # Should use default fallback for unknown prompts
    result = await langfuse_provider_with_fallbacks._get_prompt(mock_context, 'unknown', 'label')
    assert result == 'Default fallback prompt'


@pytest.mark.asyncio
async def test_langfuse_fallback_priority_order(langfuse_provider_with_fallbacks, mock_context):
    """Test that fallback prompts are used in correct priority order."""
    # Mock API failure
    langfuse_provider_with_fallbacks._langfuse.async_api.prompts.get = AsyncMock(side_effect=Exception('API failure'))

    # Test with both specific and general fallbacks available
    result = await langfuse_provider_with_fallbacks._get_prompt(mock_context, 'search', 'beta')
    assert result == 'Beta search fallback'  # Most specific

    # Test with only general fallback available
    result = await langfuse_provider_with_fallbacks._get_prompt(mock_context, 'search', 'alpha')
    assert result == 'Default fallback prompt'  # search general fallback not available, use default


@pytest.mark.asyncio
async def test_langfuse_fallback_with_provider_function(langfuse_provider_with_fallbacks, mock_context):
    """Test fallback mechanism integration with provider functions."""
    # Mock API failure
    langfuse_provider_with_fallbacks._langfuse.async_api.prompts.get = AsyncMock(side_effect=Exception('API failure'))

    # Get provider function
    provider_func = langfuse_provider_with_fallbacks.get_provider('chat', 'production')

    # Should use fallback through provider function
    result = await provider_func(mock_context)
    assert result == 'Production chat fallback'


@pytest.mark.asyncio
async def test_langfuse_normal_operation_with_fallbacks_available(
    mock_context, mock_prompt_response, mock_text_prompt_client
):
    """Test that normal operation works even when fallbacks are configured."""
    fallback_prompts = {'test_prompt_production': 'Production fallback', 'default': 'Default fallback'}

    with patch('mareo.common.prompts.langfuse_provider.get_client') as mock_get_client:
        mock_client = Mock()
        mock_client.async_api = Mock()
        mock_get_client.return_value = mock_client

        provider = LangfusePrompt(fallback_prompts=fallback_prompts)
        provider._langfuse = mock_client

        # Mock successful API call
        with patch('mareo.common.prompts.langfuse_provider.TextPromptClient', return_value=mock_text_prompt_client):
            provider._langfuse.async_api.prompts.get = AsyncMock(return_value=mock_prompt_response)

            # Should use normal API response, not fallback
            result = await provider._get_prompt(mock_context, 'test_prompt', 'production')
            assert result.compile() == 'compiled_prompt_text'
            provider._langfuse.async_api.prompts.get.assert_called_once()


@pytest.mark.asyncio
async def test_langfuse_fallback_environment_variables():
    """Test loading Langfuse fallbacks from environment variables."""
    import os

    # Set environment variables
    os.environ['FALLBACK_PROMPT_CHAT_PRODUCTION'] = 'Env production chat'
    os.environ['FALLBACK_PROMPT_DEFAULT'] = 'Env default fallback'

    try:
        with patch('mareo.common.prompts.langfuse_provider.get_client') as mock_get_client:
            mock_client = Mock()
            mock_get_client.return_value = mock_client

            provider = LangfusePrompt()

            # Should load with normalized names
            assert provider._get_fallback_prompt('chat-production') == 'Env production chat'
            assert provider._get_fallback_prompt('default') == 'Env default fallback'

    finally:
        # Clean up environment variables
        del os.environ['FALLBACK_PROMPT_CHAT_PRODUCTION']
        del os.environ['FALLBACK_PROMPT_DEFAULT']


@pytest.mark.asyncio
async def test_langfuse_fallback_failure_without_fallback():
    """Test that LangfusePrompt fails properly when no fallback is available."""
    with patch('mareo.common.prompts.langfuse_provider.get_client') as mock_get_client:
        mock_client = Mock()
        mock_client.async_api = Mock()
        mock_get_client.return_value = mock_client

        provider = LangfusePrompt()  # No fallback prompts
        provider._langfuse = mock_client

        # Mock API failure
        provider._langfuse.async_api.prompts.get = AsyncMock(side_effect=Exception('Langfuse API failure'))

        mock_context = Mock(spec=ReadonlyContext)
        mock_context.state = {}

        with pytest.raises(Exception, match='Langfuse API failure'):
            await provider._get_prompt(mock_context, 'unknown_prompt', 'label')


@pytest.mark.asyncio
async def test_langfuse_default_fallback_prompts():
    """Test that LangfusePrompt has default fallback prompts configured."""
    # The langfuse_prompt instance should have default fallbacks
    from mareo.common.prompts.langfuse_provider import langfuse_prompt

    # Check that default fallbacks are loaded
    assert langfuse_prompt._get_fallback_prompt('next-chat') is not None
    assert langfuse_prompt._get_fallback_prompt('default') is not None

    # Verify content
    next_chat_fallback = langfuse_prompt._get_fallback_prompt('next-chat')
    assert 'helpful AI assistant' in next_chat_fallback

    default_fallback = langfuse_prompt._get_fallback_prompt('default')
    assert 'helpful AI assistant' in default_fallback
