from __future__ import annotations

import json
from typing import Any, AsyncGenerator
from unittest.mock import MagicMock, patch

import pytest
from fastapi.testclient import TestClient
from google.adk.events.event import Event
from google.adk.runners import Runner
from google.genai import types

from mareo.app.main import get_fast_api_app


class _InMemorySessionService:
    def __init__(self) -> None:
        self._data: dict[str, dict[str, dict[str, dict[str, Any]]]] = {}

    async def get_session(self, app_name: str, user_id: str, session_id: str):
        return self._data.get(app_name, {}).get(user_id, {}).get(session_id)

    async def create_session(self, app_name: str, user_id: str, state=None, session_id=None):
        self._data.setdefault(app_name, {}).setdefault(user_id, {})
        if session_id is None:
            session_id = 's_test'
        sess = {
            'id': session_id,
            'app_name': app_name,
            'user_id': user_id,
            'events': [],
            'state': state or {},
        }
        self._data[app_name][user_id][session_id] = sess
        return sess


class _InMemoryArtifactService:
    def __init__(self) -> None:
        # key -> list of { version, artifact }
        self._store: dict[str, list[dict[str, Any]]] = {}

    async def save_artifact(self, app_name, user_id, session_id, filename, artifact):  # noqa: ANN001
        key = f'{app_name}:{user_id}:{session_id}:{filename}'
        versions = self._store.setdefault(key, [])
        ver = len(versions)
        versions.append({'version': ver, 'artifact': artifact})
        return ver

    async def load_artifact(self, app_name, user_id, session_id, filename, version=None):  # noqa: ANN001
        key = f'{app_name}:{user_id}:{session_id}:{filename}'
        if key not in self._store:
            return None
        versions = self._store[key]
        if version is None:
            return versions[-1]['artifact']
        for v in versions:
            if v['version'] == version:
                return v['artifact']
        return None

    async def list_artifact_keys(self, app_name, user_id, session_id):  # noqa: ANN001
        prefix = f'{app_name}:{user_id}:{session_id}:'
        return [k.split(':', 3)[-1] for k in self._store.keys() if k.startswith(prefix)]

    async def list_versions(self, app_name, user_id, session_id, filename):  # noqa: ANN001
        key = f'{app_name}:{user_id}:{session_id}:{filename}'
        if key not in self._store:
            return []
        return [v['version'] for v in self._store[key]]

    async def delete_artifact(self, app_name, user_id, session_id, filename):  # noqa: ANN001
        key = f'{app_name}:{user_id}:{session_id}:{filename}'
        if key in self._store:
            del self._store[key]


class _FakeRunner(Runner):
    last_message_text: str | None = None

    def __init__(self, *args, **kwargs) -> None:  # type: ignore[no-untyped-def]
        self.app_name = kwargs.get('app_name') or (args[0] if args else 'app')
        self.session_service = kwargs.get('session_service') or MagicMock()

    async def run_async(  # type: ignore[override]
        self,
        user_id: str,
        session_id: str,
        new_message: types.Content,
        run_config=None,
    ) -> AsyncGenerator[Event, None]:
        # Capture injected text for assertion
        text_parts = [p.text for p in new_message.parts if p.text]
        _FakeRunner.last_message_text = '\n'.join(text_parts)

        # Write a prebuilt group result into session.state (pointer /Company)
        session = await self.session_service.get_session(app_name=self.app_name, user_id=user_id, session_id=session_id)
        state = session['state']
        state['fill_group__0__Company'] = json.dumps(
            {
                '2023': {
                    'value': 100.0,
                    'claims': [
                        {
                            'level': '2',
                            'kind': 'paraphrase',
                            'text': 'rev 100',
                            'confidence': 0.9,
                            'evidence': [{'source_id': 'S1', 'span_id': 'p0b0s1'}],
                        }
                    ],
                }
            }
        )

        yield Event(
            author='parallel_fill',
            invocation_id='inv1',
            content=types.Content(role='model', parts=[types.Part.from_text(text='ok')]),
        )


@pytest.fixture()
def app_client():
    session_service = _InMemorySessionService()
    artifact_service = _InMemoryArtifactService()

    with (
        patch('mareo.app.services.create_session_service', return_value=session_service),
        patch('mareo.app.services.create_artifact_service', return_value=artifact_service),
        patch('mareo.app.services.create_memory_service', return_value=MagicMock()),
        patch('mareo.app.main.AgentLoader', return_value=MagicMock()),
        patch('opentelemetry.trace.get_tracer_provider') as mock_tracer_provider,
        patch('mareo.app.api.v1.endpoints.fill.Runner', _FakeRunner),
        patch('google.adk.runners.Runner', _FakeRunner),
    ):
        mock_provider = MagicMock()
        mock_provider.add_span_processor = MagicMock()
        mock_tracer_provider.return_value = mock_provider

        app = get_fast_api_app(
            agents_dir='.',
            web=False,
            session_service_uri='',
            artifact_service_uri='',
            memory_service_uri='',
            allow_origins=['*'],
            a2a=False,
        )
        # Inject our services explicitly (not strictly necessary but clearer)
        app.state.session_service = session_service
        app.state.artifact_service = artifact_service

        client = TestClient(app)
        yield client


def _make_pdf_json(title: str = '标题') -> dict[str, Any]:
    return {
        'pdf_info': [
            {
                'page_idx': 0,
                'para_blocks': [
                    {
                        'index': 0,
                        'type': 'text',
                        'lines': [
                            {
                                'spans': [
                                    {
                                        'type': 'text',
                                        'content': '第一句。第二句；第三句',
                                    }
                                ]
                            }
                        ],
                    },
                    {
                        'index': 1,
                        'type': 'title',
                        'level': 2,
                        'lines': [
                            {
                                'spans': [
                                    {
                                        'type': 'text',
                                        'content': title,
                                    }
                                ]
                            }
                        ],
                    },
                ],
            }
        ]
    }


def test_evidence_upload_and_manifest_and_pool(app_client: TestClient):
    app_name, user_id, session_id = 'test_app', 'u1', 's1'
    # Ensure session exists (upload also supports auto-create in API, but we pre-create here for stability)
    resp = app_client.post(f'/apps/{app_name}/users/{user_id}/sessions/{session_id}', json={'state': {}})
    assert resp.status_code == 200

    # Prepare two JSON files
    data1 = _make_pdf_json('文档A')
    data2 = _make_pdf_json('文档B')

    files = [
        ('files', ('a.json', json.dumps(data1), 'application/json')),
        ('files', ('b.json', json.dumps(data2), 'application/json')),
    ]
    resp = app_client.post(
        f'/apps/{app_name}/users/{user_id}/sessions/{session_id}/evidence/upload',
        files=files,
    )
    assert resp.status_code == 200
    body = resp.json()

    manifest = body['manifest']
    sources = manifest['sources']
    assert len(sources) == 2
    short_ids = {s['short_id'] for s in sources}
    assert short_ids == {'S1', 'S2'}
    # filename to short id mapping exists
    mapping = {s['filename']: s['short_id'] for s in sources}
    assert mapping['a.json'] == 'S1'
    assert mapping['b.json'] == 'S2'

    # Pool versions returned
    assert 'pool' in body and body['pool']['md_version'] >= 0


def test_fill_path_uses_pool_and_empty_source_ids(app_client: TestClient):
    app_name, user_id, session_id = 'test_app', 'u2', 's2'
    # Pre-create session for stability
    resp = app_client.post(f'/apps/{app_name}/users/{user_id}/sessions/{session_id}', json={'state': {}})
    assert resp.status_code == 200

    # Upload one file
    data = _make_pdf_json('填报文档')
    files = [('files', ('c.json', json.dumps(data), 'application/json'))]
    resp = app_client.post(
        f'/apps/{app_name}/users/{user_id}/sessions/{session_id}/evidence/upload',
        files=files,
    )
    assert resp.status_code == 200

    # Prepare schema and call fill with empty source_ids (use all)
    schema = {
        '$schema': 'https://json-schema.org/draft/2020-12/schema',
        'title': '公司营收',
        'type': 'object',
        'properties': {
            'Company': {
                'type': 'object',
                'properties': {
                    '2023': {'type': 'number'},
                },
                'required': ['2023'],
                'additionalProperties': False,
            }
        },
        'required': ['Company'],
        'additionalProperties': False,
    }
    payload = {
        'schema': schema,
        'source_ids': [],  # empty -> use all
        'instructions': '统一USD_M，保留两位',
    }
    resp = app_client.post(f'/api/v1/apps/{app_name}/users/{user_id}/sessions/{session_id}/fill', json=payload)
    assert resp.status_code == 200
    body = resp.json()

    # Data merged from fake runner
    assert body['data']['Company']['2023'] == 100.0

    # The injected message included evidence pool lines (prefix with Sx)::
    assert _FakeRunner.last_message_text is not None
    assert '这是所有的证据池片段：' in _FakeRunner.last_message_text
    assert 'S1::' in _FakeRunner.last_message_text or 'S2::' in _FakeRunner.last_message_text


def test_evidence_upload_auto_creates_session(app_client: TestClient):
    app_name, user_id, session_id = 'auto_app', 'u_auto', 's_auto'
    # No pre-create, upload should create session implicitly
    data = _make_pdf_json('AutoCreate')
    files = [('files', ('auto.json', json.dumps(data), 'application/json'))]
    resp = app_client.post(
        f'/apps/{app_name}/users/{user_id}/sessions/{session_id}/evidence/upload',
        files=files,
    )
    assert resp.status_code == 200
    body = resp.json()
    assert 'manifest' in body and len(body['manifest']['sources']) == 1
