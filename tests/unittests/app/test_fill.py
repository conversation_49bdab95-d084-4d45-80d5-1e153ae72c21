from __future__ import annotations

import json
from typing import Any, AsyncGenerator
from unittest.mock import MagicMock, patch

import pytest
from fastapi.testclient import TestClient
from google.adk.events.event import Event
from google.adk.runners import Runner
from google.genai import types

from mareo.app.main import get_fast_api_app


class _Session:
    def __init__(self, *, id: str, app_name: str, user_id: str, state: dict[str, Any] | None = None):
        self.id = id
        self.app_name = app_name
        self.user_id = user_id
        self.events: list[Any] = []
        self.state: dict[str, Any] = state or {}


class _InMemorySessionService:
    def __init__(self) -> None:
        self._data: dict[str, dict[str, dict[str, _Session]]] = {}

    async def get_session(self, app_name: str, user_id: str, session_id: str):
        return self._data.get(app_name, {}).get(user_id, {}).get(session_id)

    async def create_session(self, app_name: str, user_id: str, state=None, session_id=None):
        self._data.setdefault(app_name, {}).setdefault(user_id, {})
        if session_id is None:
            session_id = 's_test'
        sess = _Session(id=session_id, app_name=app_name, user_id=user_id, state=state or {})
        self._data[app_name][user_id][session_id] = sess
        return sess


class _FakeRunner(Runner):
    last_message_text: str | None = None

    def __init__(self, *args, **kwargs) -> None:  # type: ignore[no-untyped-def]
        # Mimic real signature
        self.app_name = kwargs.get('app_name') or (args[0] if args else 'fill_pipeline')
        self.session_service = kwargs.get('session_service') or MagicMock()

    async def run_async(  # type: ignore[override]
        self,
        user_id: str,
        session_id: str,
        new_message: types.Content,
        run_config=None,
    ) -> AsyncGenerator[Event, None]:
        # Capture injected text for assertion
        text_parts = [p.text for p in new_message.parts if p.text]
        _FakeRunner.last_message_text = '\n'.join(text_parts)

        # Write a prebuilt group result into session.state
        session = await self.session_service.get_session(app_name=self.app_name, user_id=user_id, session_id=session_id)
        state = session.state
        # Single group at pointer /Company → state key fill_group__0__Company
        state['fill_group__0__Company'] = json.dumps(
            {
                '2023': {
                    'value': 100.0,
                    'claims': [
                        {
                            'level': '2',
                            'kind': 'paraphrase',
                            'text': 'rev 100',
                            'confidence': 0.9,
                            'evidence': [{'source_id': 'S1', 'span_id': 'O1'}],
                        }
                    ],
                },
                '2024': {
                    'value': 120.0,
                    'claims': [
                        {
                            'level': '1',
                            'kind': 'derived',
                            'text': 'rev 120',
                            'confidence': 0.8,
                            'evidence': [],
                            'derived_from': {'reasoning': '同比+20%'},
                        }
                    ],
                },
            }
        )

        # Yield a dummy event
        yield Event(
            author='parallel_fill',
            invocation_id='inv1',
            content=types.Content(role='model', parts=[types.Part.from_text(text='ok')]),
        )


class _StubAsyncClient:
    def __init__(self) -> None:
        self._resp = type(
            'Resp',
            (),
            {
                'content': b'DEMO',
                'raise_for_status': (lambda self: None),
            },
        )()

    async def __aenter__(self):  # noqa: D401
        return self

    async def __aexit__(self, exc_type, exc, tb):  # noqa: D401
        return False

    async def get(self, url: str):  # noqa: D401
        return self._resp


@pytest.fixture()
def app_client():
    session_service = _InMemorySessionService()

    with (
        patch('mareo.app.services.create_session_service', return_value=session_service),
        patch('mareo.app.services.create_artifact_service', return_value=MagicMock()),
        patch('mareo.app.services.create_memory_service', return_value=MagicMock()),
        patch('mareo.app.main.AgentLoader', return_value=MagicMock()),
        patch('opentelemetry.trace.get_tracer_provider') as mock_tracer_provider,
        patch('mareo.app.api.v1.endpoints.fill.Runner', _FakeRunner),
        patch('mareo.app.api.v1.endpoints.fill.httpx.AsyncClient', _StubAsyncClient),
    ):
        mock_provider = MagicMock()
        mock_provider.add_span_processor = MagicMock()
        mock_tracer_provider.return_value = mock_provider

        app = get_fast_api_app(
            agents_dir='.',
            web=False,
            session_service_uri='',
            artifact_service_uri='',
            memory_service_uri='',
            allow_origins=['*'],
            a2a=False,
        )
        # Inject our session service explicitly
        app.state.session_service = session_service
        client = TestClient(app)
        yield client


def test_fill_endpoint_basic(app_client: TestClient):
    schema = {
        '$schema': 'https://json-schema.org/draft/2020-12/schema',
        'title': '公司营收（宽表）',
        'type': 'object',
        'properties': {
            'Company': {
                'type': 'object',
                'properties': {
                    '2023': {'type': 'number'},
                    '2024': {'type': 'number'},
                },
                'required': ['2023', '2024'],
                'additionalProperties': False,
            }
        },
        'required': ['Company'],
        'additionalProperties': False,
    }

    payload = {
        'schema': schema,
        'source_ids': ['S1', 'S2'],
        'instructions': '统一USD_M，保留两位',
        'retrieval': {'enable': True, 'topk': 3},
        'limits': {'numeric_unit': 'USD_M', 'rounding': 2},
        'meta': {'task_label': 'Q2'},
    }

    resp = app_client.post('/api/v1/fill', json=payload)
    assert resp.status_code == 200
    body = resp.json()

    # Data merged correctly
    assert body['data']['Company']['2023'] == 100.0
    assert body['data']['Company']['2024'] == 120.0

    # Provenance extracted from claims under corresponding pointers
    p_2023 = body['provenance']['/Company/2023'][0]
    assert p_2023['kind'] in ('paraphrase', 'derived')
    assert 0 <= p_2023['confidence'] <= 1
    assert p_2023['evidence'][0]['sourceId'] == 'S1'
    assert p_2023['evidence'][0]['spanId'] == 'O1'

    # Meta fields present
    assert body['meta']['schemaHash'].startswith('sha256:')
    assert body['meta']['unitPolicy'] == 'USD_M'
    assert body['meta']['taskId'].startswith('t_')

    # The injected message included allowed sources and instructions
    assert (
        '这是所有的证据池片段：' in _FakeRunner.last_message_text
        or '仅可引用以下来源：[S1, S2]' in _FakeRunner.last_message_text
    )
    assert '统一USD_M' in _FakeRunner.last_message_text
