import React, { useEffect, useMemo, useRef, useState } from "react";
// pdf.js
// Using bundler-friendly worker setup. Vite resolves '?url' to a file URL string.
// Types for '?url' are declared in vite-env.d.ts
import { GlobalWorkerOptions, getDocument, PDFDocumentProxy } from "pdfjs-dist";
// @ts-ignore - typed via vite-env.d.ts
import pdfjsWorkerUrl from "pdfjs-dist/build/pdf.worker.mjs?url";
GlobalWorkerOptions.workerSrc = pdfjsWorkerUrl as unknown as string;

// --- Types (lightweight, tolerant of missing fields) ---
type Span = { type?: string; content?: string; bbox?: [number, number, number, number] };
type Line = { bbox?: [number, number, number, number]; spans?: Span[] };
type ParaBlock = {
  type?: string; // text | title | image | table ...
  bbox?: [number, number, number, number];
  lines?: Line[];
  blocks?: any[]; // nested (images/tables)
  index?: number;
};

type PageInfo = {
  para_blocks?: ParaBlock[];
  page_size?: [number, number];
  page_idx: number;
};

type MinerUJson = {
  pdf_info?: PageInfo[];
};

type TextBlock = {
  id: string;
  pageIdx: number;
  pageW: number;
  pageH: number;
  bbox: [number, number, number, number];
  text: string;
  kind: string;
};

// --- Helpers ---
const sanitizeText = (s?: string) => (s || "").replace(/\s+/g, " ").trim();
function splitIntoSentences(text: string): string[] {
  if (!text) return [];
  const delimiters = new Set(["。", "！", "？", "；", "!", "?", ";"]);
  const sentences: string[] = [];
  const current: string[] = [];
  for (let i = 0; i < text.length; i++) {
    const ch = text[i];
    current.push(ch);
    if (ch === "!" && i + 1 < text.length && text[i + 1] === "[") {
      // avoid breaking on markdown image opener '!['
      continue;
    }
    if (delimiters.has(ch)) {
      const s = sanitizeText(current.join(""));
      if (s) sentences.push(s);
      current.length = 0;
    }
  }
  const tail = sanitizeText(current.join(""));
  if (tail) sentences.push(tail);
  return sentences;
}
function getBlockText(pb: ParaBlock): string {
  let out = "";
  const collect = (node: any) => {
    const lines = node?.lines || [];
    for (const ln of lines) {
      const spans = ln?.spans || [];
      for (const sp of spans) {
        out += sp?.content || "";
      }
    }
    const children = node?.blocks || [];
    for (const c of children) collect(c);
  };
  collect(pb as any);
  return sanitizeText(out);
}
function parseSpanId(id: string): { page: number; block: number; span: number } | null {
  const raw = id.trim();
  let m = /^p(\d+)b(\d+)s(\d+)$/.exec(raw);
  if (!m) {
    // tolerate prefixes like "S1::p0b3s12:: ..." or any text containing p..b..s..
    const m2 = /p(\d+)b(\d+)s(\d+)/.exec(raw);
    if (!m2) return null;
    m = m2;
  }
  return { page: parseInt(m[1], 10), block: parseInt(m[2], 10), span: parseInt(m[3], 10) };
}

function collectTextBlocks(doc: MinerUJson): { blocks: TextBlock[]; stats: { pages: number; blocks: number } } {
  const pages = doc.pdf_info || [];
  const blocks: TextBlock[] = [];

  for (const page of pages) {
    const pageIdx = page.page_idx ?? 0;
    const [pageW, pageH] = page.page_size || [595, 841];
    const paras = page.para_blocks || [];

    const pushBlock = (kind: string, bbox: [number, number, number, number], text: string, customId?: string) => {
      blocks.push({
        id: customId ?? `p${pageIdx}-${kind}-${blocks.length}`,
        pageIdx,
        pageW,
        pageH,
        bbox,
        text,
        kind,
      });
    };

    const walkBlock = (pb: ParaBlock, blockIdxInPage: number | null) => {
      const kind = pb.type || "text";
      if (kind === "text" || kind === "title") {
        // sentence-level ids aligned with external generator: p{page}b{block}s{j} (1-based)
        const fullText = getBlockText(pb);
        let sentences: string[] = [];
        if (kind === "title") {
          if (fullText) sentences = [fullText];
        } else {
          sentences = splitIntoSentences(fullText);
        }
        if (!sentences.length) sentences = fullText ? [fullText] : [];
        const bboxForBlock = (pb.bbox || [0, 0, 0, 0]) as [number, number, number, number];
        if (blockIdxInPage !== null) {
          for (let j = 0; j < sentences.length; j++) {
            const sid = `p${pageIdx}b${blockIdxInPage}s${j + 1}`; // 1-based
            pushBlock("sentence", bboxForBlock, sentences[j], sid);
          }
        }
        // keep line-level boxes for visualization/search
        const lines = pb.lines || [];
        for (const ln of lines) {
          const text = sanitizeText((ln.spans || []).map(sp => sp.content || "").join(""));
          if (!text) continue;
          const bbox = (ln.bbox || pb.bbox || [0, 0, 0, 0]) as [number, number, number, number];
          pushBlock(kind, bbox, text);
        }
      } else if ((kind === "image" || kind === "table") && pb.bbox) {
        // Include non-text blocks so they can be shown when "show all boxes" is enabled
        // also provide a sentence-like id with j=1 so p{page}b{block}s1 works
        const bbox = pb.bbox as [number, number, number, number];
        pushBlock(kind, bbox, kind);
        if (blockIdxInPage !== null) {
          const sid = `p${pageIdx}b${blockIdxInPage}s1`;
          pushBlock("sentence", bbox, kind, sid);
        }
      }

      // Recurse nested blocks if present (e.g., tables/images may have child blocks)
      for (const child of (pb.blocks || [])) {
        walkBlock(child as ParaBlock, blockIdxInPage);
      }
    };

    for (let blockIdx = 0; blockIdx < paras.length; blockIdx++) {
      const pb = paras[blockIdx];
      const blockIndex = (pb && typeof pb.index === 'number') ? (pb.index as number) : blockIdx;
      walkBlock(pb, blockIndex);
    }
  }

  return { blocks, stats: { pages: pages.length, blocks: blocks.length } };
}

function useDebounced<T>(value: T, ms = 250) {
  const [v, setV] = useState(value);
  useEffect(() => {
    const t = setTimeout(() => setV(value), ms);
    return () => clearTimeout(t);
  }, [value, ms]);
  return v;
}

// --- Page Canvas ---
function PageCanvas({
  pageIdx,
  pageSize,
  allBlocks,
  matches,
  selectedId,
  scale,
  showAllBoxes,
}: {
  pageIdx: number;
  pageSize: [number, number];
  allBlocks: TextBlock[]; // all blocks on this page
  matches: Set<string>; // matched block ids
  selectedId?: string | null;
  scale: number; // display width relative to original width
  showAllBoxes: boolean;
}) {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const [w, h] = pageSize;
    const dpr = (typeof window !== 'undefined' && window.devicePixelRatio) ? window.devicePixelRatio : 1;
    const cssW = Math.max(1, Math.floor(w * scale));
    const cssH = Math.max(1, Math.floor(h * scale));
    canvas.width = Math.max(1, Math.floor(cssW * dpr));
    canvas.height = Math.max(1, Math.floor(cssH * dpr));
    // Ensure CSS size matches the overlay container to keep bbox aligned with PDF
    (canvas.style as any).width = `${cssW}px`;
    (canvas.style as any).height = `${cssH}px`;
    const ctx = canvas.getContext("2d");
    if (!ctx) return;
    ctx.setTransform(dpr, 0, 0, dpr, 0, 0); // work in CSS pixel space
    // Clear
    ctx.clearRect(0, 0, cssW, cssH);

    // Draw boxes
    const blocksToShow = showAllBoxes ? allBlocks : allBlocks.filter(b => matches.has(b.id));
    for (const b of blocksToShow) {
      const [x1, y1, x2, y2] = b.bbox;
      const x = x1 * scale;
      const y = y1 * scale;
      const w = (x2 - x1) * scale;
      const h = (y2 - y1) * scale;

      const isSelected = selectedId === b.id;
      const isMatch = matches.has(b.id);

      // Fill lightly for matches
      if (isMatch) {
        ctx.save();
        ctx.globalAlpha = isSelected ? 0.25 : 0.12;
        ctx.fillStyle = "#60a5fa"; // blue-400
        ctx.fillRect(x, y, w, h);
        ctx.restore();
      }

      // Stroke for all visible boxes
      ctx.save();
      ctx.lineWidth = isSelected ? 2.5 : 1;
      ctx.setLineDash(isMatch ? [] : [4, 3]);
      ctx.strokeStyle = isSelected ? "#2563eb" : isMatch ? "#3b82f6" : "#9ca3af"; // blue-600/500/gray-400
      ctx.strokeRect(x + 0.5, y + 0.5, w - 1, h - 1);
      ctx.restore();
    }
  }, [pageSize, allBlocks, matches, selectedId, scale, showAllBoxes]);

  return (
    // Overlay-only canvas. PDF is rendered in a sibling canvas below
    <canvas ref={canvasRef} className="absolute left-0 top-0 pointer-events-none block" />
  );
}

// Renders a PDF page using pdf.js and overlays the detection canvas on top
function PdfPageView({
  pageIdx,
  jsonPageSize,
  perPageBlocks,
  matches,
  selectedId,
  scale,
  showAllBoxes,
  pdfDoc,
}: {
  pageIdx: number;
  jsonPageSize: [number, number];
  perPageBlocks: TextBlock[];
  matches: Set<string>;
  selectedId?: string | null;
  scale: number;
  showAllBoxes: boolean;
  pdfDoc: PDFDocumentProxy | null;
}) {
  const pdfCanvasRef = useRef<HTMLCanvasElement | null>(null);
  const [renderKey, setRenderKey] = useState(0);

  // Render PDF page whenever pdfDoc/scale changes (use devicePixelRatio to improve resolution)
  useEffect(() => {
    let cancelled = false;
    const run = async () => {
      const canvas = pdfCanvasRef.current;
      if (!canvas) return;
      const [jsonW, jsonH] = jsonPageSize;
      const dpr = (typeof window !== 'undefined' && window.devicePixelRatio) ? window.devicePixelRatio : 1;
      const targetW = Math.max(1, Math.floor(jsonW * scale));
      const targetH = Math.max(1, Math.floor(jsonH * scale));

      if (!pdfDoc) {
        // No PDF: just size the canvas, leave blank
        canvas.width = Math.max(1, Math.floor(targetW * dpr));
        canvas.height = Math.max(1, Math.floor(targetH * dpr));
        (canvas.style as any).width = `${targetW}px`;
        (canvas.style as any).height = `${targetH}px`;
        const ctx = canvas.getContext("2d");
        if (ctx) {
          ctx.setTransform(dpr, 0, 0, dpr, 0, 0);
          ctx.clearRect(0, 0, targetW, targetH);
          ctx.fillStyle = "#ffffff";
          ctx.fillRect(0, 0, targetW, targetH);
          ctx.strokeStyle = "#e5e7eb";
          ctx.lineWidth = 1;
          ctx.strokeRect(0.5, 0.5, targetW - 1, targetH - 1);
        }
        return;
      }

      try {
        const page = await pdfDoc.getPage(pageIdx + 1);
        const viewport1 = page.getViewport({ scale: 1 });
        const pdfW1 = viewport1.width;
        const renderScale = (targetW / pdfW1) * dpr; // render at higher pixel ratio
        const viewport = page.getViewport({ scale: renderScale });
        canvas.width = Math.floor(viewport.width);
        canvas.height = Math.floor(viewport.height);
        // Set CSS size to targetW/targetH so overlay uses same CSS pixels
        (canvas.style as any).width = `${targetW}px`;
        (canvas.style as any).height = `${targetH}px`;
        const ctx = canvas.getContext("2d");
        if (!ctx) return;
        const task = page.render({ canvasContext: ctx, viewport });
        await task.promise;
        if (!cancelled) setRenderKey(k => k + 1);
      } catch (e) {
        // eslint-disable-next-line no-console
        console.error("Failed to render PDF page", e);
      }
    };
    run();
    return () => {
      cancelled = true;
    };
  }, [pdfDoc, pageIdx, jsonPageSize, scale]);

  const [jsonW, jsonH] = jsonPageSize;
  const widthPx = Math.max(1, Math.floor(jsonW * scale));
  const heightPx = Math.max(1, Math.floor(jsonH * scale));

  return (
    <section id={`page-${pageIdx}`} className="bg-white rounded-2xl shadow p-4">
      <div className="flex items-center justify-between mb-3">
        <div className="font-medium">第 {pageIdx + 1} 页</div>
        <div className="text-xs text-gray-500">渲染尺寸：{widthPx} × {heightPx}</div>
      </div>
      <div className="w-full flex justify-center">
        <div className="relative" style={{ width: widthPx, height: heightPx }}>
          <canvas ref={pdfCanvasRef} className="rounded-2xl shadow-sm" style={{ width: widthPx, height: heightPx }} />
          <div className="absolute left-0 top-0" style={{ width: widthPx, height: heightPx }}>
            <PageCanvas
              pageIdx={pageIdx}
              pageSize={jsonPageSize}
              allBlocks={perPageBlocks}
              matches={showAllBoxes ? new Set(perPageBlocks.map(b => b.id)) : matches}
              selectedId={selectedId}
              scale={scale}
              showAllBoxes={showAllBoxes}
            />
          </div>
        </div>
      </div>
    </section>
  );
}

// --- Main App ---
export default function TextLocatorPOC() {
  const [doc, setDoc] = useState<MinerUJson | null>(null);
  const [blocks, setBlocks] = useState<TextBlock[]>([]);
  const [stats, setStats] = useState<{ pages: number; blocks: number }>({ pages: 0, blocks: 0 });
  const [query, setQuery] = useState("");
  const [spanQuery, setSpanQuery] = useState("");
  const [selectedId, setSelectedId] = useState<string | null>(null);
  const [scale, setScale] = useState(0.9); // relative to original PDF width
  const [showAllBoxes, setShowAllBoxes] = useState(false);
  const [pdfDoc, setPdfDoc] = useState<PDFDocumentProxy | null>(null);
  const [searchOpen, setSearchOpen] = useState<boolean>(false);

  // Backend + Session inputs
  const [backendUrl, setBackendUrl] = useState<string>(
    (typeof localStorage !== 'undefined' && localStorage.getItem('mareo_backend')!) || (typeof window !== 'undefined' ? window.location.origin : "http://localhost:3000")
  );
  const [appName, setAppName] = useState<string>((typeof localStorage !== 'undefined' && localStorage.getItem('mareo_app')!) || "fill_pipeline");
  const [userId, setUserId] = useState<string>((typeof localStorage !== 'undefined' && localStorage.getItem('mareo_user')!) || "user");
  const [sessionId, setSessionId] = useState<string>(() => {
    try {
      if (typeof crypto !== 'undefined' && 'randomUUID' in crypto) {
        return crypto.randomUUID();
      }
    } catch {}
    return 's_' + Math.random().toString(36).slice(2, 10) + Date.now().toString(36);
  });

  useEffect(() => {
    try { localStorage.setItem('mareo_backend', backendUrl); } catch {}
  }, [backendUrl]);
  useEffect(() => {
    try { localStorage.setItem('mareo_app', appName); } catch {}
  }, [appName]);
  useEffect(() => {
    try { localStorage.setItem('mareo_user', userId); } catch {}
  }, [userId]);
  useEffect(() => {
    try { localStorage.setItem('mareo_session', sessionId); } catch {}
  }, [sessionId]);

  // Multiple docs (JSON + PDF) management
  type LoadedJson = { file: File; doc: MinerUJson; blocks: TextBlock[]; stats: { pages: number; blocks: number } };
  const [jsonFiles, setJsonFiles] = useState<Record<string, LoadedJson>>({}); // key: json filename (basename)
  const [pdfFiles, setPdfFiles] = useState<Record<string, PDFDocumentProxy>>({}); // key: pdf filename (basename)
  const [manifestMap, setManifestMap] = useState<Record<string, string>>({}); // shortId -> filename (json)

  // Fill outputs
  const [fillData, setFillData] = useState<any>(null);
  const [fillProv, setFillProv] = useState<Record<string, any[]>>({});

  function basename(path: string): string {
    return path.split(/[/\\]/).pop() || path;
  }

  function jsonNameToPdfName(jsonName: string): string {
    const base = jsonName.replace(/^.*[/\\]/, '');
    // Remove suffix like _MinerU__20250813062905.json → .pdf
    const m = base.match(/^(.*?)(?:_MinerU__\d+)?\.json$/);
    if (m) return `${m[1]}.pdf`;
    return base.replace(/\.json$/i, '.pdf');
  }

  // Read JSON via upload
  const onUpload = (file: File) => {
    const reader = new FileReader();
    reader.onload = () => {
      try {
        const json = JSON.parse(String(reader.result || "{}"));
        const { blocks, stats } = collectTextBlocks(json);
        const name = basename(file.name);
        setJsonFiles(prev => ({ ...prev, [name]: { file, doc: json, blocks, stats } }));
        // If no active doc, set as current
        setDoc(d => d || json);
        setBlocks(b => (b.length ? b : blocks));
        setStats(s => (s.blocks ? s : stats));
        setSelectedId(null);
      } catch (e) {
        alert("JSON 解析失败: " + (e as Error).message);
      }
    };
    reader.readAsText(file);
  };

  // Read PDF via upload
  const onUploadPdf = async (file: File) => {
    try {
      const arrayBuf = await file.arrayBuffer();
      const loadingTask = getDocument({ data: arrayBuf });
      const pdf = await loadingTask.promise;
      const name = basename(file.name);
      setPdfFiles(prev => ({ ...prev, [name]: pdf }));
      // If current doc has a matching pdf, set it
      if (doc) {
        // try find json name -> pdf
        const jsonEntry = Object.entries(jsonFiles)[0];
        if (jsonEntry) {
          const pdfNameExpect = jsonNameToPdfName(jsonEntry[0]);
          if (pdfNameExpect === name) setPdfDoc(pdf);
        } else {
          setPdfDoc(p => p || pdf);
        }
      } else {
        setPdfDoc(p => p || pdf);
      }
      setSelectedId(null);
    } catch (e) {
      alert("PDF 加载失败: " + (e as Error).message);
    }
  };

  function switchToJson(filename: string) {
    const entry = jsonFiles[filename];
    if (!entry) return;
    setDoc(entry.doc);
    setBlocks(entry.blocks);
    setStats(entry.stats);
    // auto-pick pdf
    const pdfName = jsonNameToPdfName(filename);
    if (pdfFiles[pdfName]) setPdfDoc(pdfFiles[pdfName]);
  }

  async function uploadAllJsonToBackend() {
    const fList = Object.values(jsonFiles).map(j => j.file);
    if (!fList.length) { alert('请先导入至少一个 JSON'); return; }
    const fd = new FormData();
    for (const f of fList) fd.append('files', f, basename(f.name));
    const url = `${backendUrl}/apps/${encodeURIComponent(appName)}/users/${encodeURIComponent(userId)}/sessions/${encodeURIComponent(sessionId)}/evidence/upload`;
    try {
      const res = await fetch(url, { method: 'POST', body: fd });
      if (!res.ok) {
        const t = await res.text();
        throw new Error(`上传失败: ${res.status} ${t}`);
      }
      const body = await res.json();
      const map: Record<string, string> = {};
      for (const s of (body?.manifest?.sources || [])) {
        if (s?.short_id && s?.filename) map[s.short_id] = s.filename;
      }
      setManifestMap(map);
      alert('上传成功，manifest 已更新');
    } catch (e) {
      alert((e as Error).message);
    }
  }

  const [schemaText, setSchemaText] = useState<string>(`{
  "$schema": "https://json-schema.org/draft/2020-12/schema",
  "title": "公司营收",
  "type": "object",
  "properties": {
    "美的": {
      "type": "object",
      "properties": { "2024": { "type": "number" } },
      "required": ["2024"],
      "additionalProperties": false
    }
  },
  "required": ["Company"],
  "additionalProperties": false
}`);
  const [instructions, setInstructions] = useState<string>('统一亿元，保留两位');

  async function runFill() {
    let schemaObj: any;
    try { schemaObj = JSON.parse(schemaText); } catch (e) { alert('Schema 解析失败'); return; }
    const url = `${backendUrl}/api/v1/apps/${encodeURIComponent(appName)}/users/${encodeURIComponent(userId)}/sessions/${encodeURIComponent(sessionId)}/fill_stream`;
    try {
      const controller = new AbortController();
      const timeoutMs = 5 * 60 * 1000; // 5 minutes
      const timer = setTimeout(() => controller.abort(), timeoutMs);
      const res = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ schema: schemaObj, source_ids: [], instructions }),
        signal: controller.signal,
      });
      clearTimeout(timer);
      if (!res.ok) { const t = await res.text(); throw new Error(`Fill 失败: ${res.status} ${t}`); }
      const body = await res.json();
      setFillData(body?.data || null);
      setFillProv(body?.provenance || {});
    } catch (e) {
      const err = e as any;
      if (err?.name === 'AbortError') {
        alert('Fill 请求超时，请稍后重试。');
      } else {
        alert((e as Error).message);
      }
    }
  }

  function pointerHasProv(ptr: string): boolean { return !!fillProv && !!(fillProv as any)[ptr]; }
  function getPointerProv(ptr: string): any[] { return ((fillProv as any)[ptr] || []) as any[]; }

  function EvidenceList({ items }: { items: any[] }) {
    return (
      <ul className="mt-1 space-y-1">
        {items.map((it, idx) => (
          <li key={idx} className="text-xs text-gray-600">
            <div className="font-medium">[{it.level}] {it.kind} · conf={it.confidence}</div>
            <div className="text-gray-500 mb-1">{it.text}</div>
            <div className="flex flex-wrap gap-2">
              {(it.evidence || []).map((ev: any, j: number) => (
                <button
                  key={j}
                  className="px-2 py-0.5 rounded bg-blue-50 text-blue-700 hover:bg-blue-100"
                  title={`sourceId=${ev.sourceId} spanId=${ev.spanId}`}
                  onClick={() => {
                    const sid = String(ev?.sourceId || ev?.source_id || '');
                    const span = String(ev?.spanId || ev?.span_id || '');
                    if (!sid || !span) return;
                    const filename = manifestMap[sid];
                    if (filename) switchToJson(filename);
                    setSelectedId(span);
                  }}
                >{`${ev.sourceId || ev.source_id} · ${ev.spanId || ev.span_id}`}</button>
              ))}
            </div>
          </li>
        ))}
      </ul>
    );
  }

  function DataView({ obj, ptr }: { obj: any; ptr: string }) {
    if (obj === null || typeof obj !== 'object' || Array.isArray(obj)) {
      return (
        <div className="flex items-center gap-2">
          <code className="text-sm">{JSON.stringify(obj)}</code>
          {pointerHasProv(ptr) && <EvidenceList items={getPointerProv(ptr)} />}
        </div>
      );
    }
    const entries = Object.entries(obj);
    return (
      <ul className="ml-4 border-l border-gray-200 pl-3 space-y-1">
        {entries.map(([k, v]) => {
          const childPtr = ptr === '/' ? `/${k}` : `${ptr}/${k}`;
          const isLeaf = v === null || typeof v !== 'object' || Array.isArray(v);
          return (
            <li key={k} className="">
              <div className="flex items-start gap-2">
                <span className="font-mono text-xs text-gray-500">{k}:</span>
                {isLeaf ? (
                  <div className="flex items-center gap-2">
                    <code className="text-sm">{JSON.stringify(v)}</code>
                    {pointerHasProv(childPtr) && <EvidenceList items={getPointerProv(childPtr)} />}
                  </div>
                ) : (
                  <DataView obj={v} ptr={childPtr} />
                )}
              </div>
            </li>
          );
        })}
      </ul>
    );
  }

  const debouncedQuery = useDebounced(query, 200);

  const { pages, pageMap } = useMemo(() => {
    const pages = (doc?.pdf_info || []).map(p => ({
      pageIdx: p.page_idx ?? 0,
      size: (p.page_size || [595, 841]) as [number, number],
    })).sort((a, b) => a.pageIdx - b.pageIdx);
    const pageMap = new Map<number, TextBlock[]>();
    for (const b of blocks) {
      if (!pageMap.has(b.pageIdx)) pageMap.set(b.pageIdx, []);
      pageMap.get(b.pageIdx)!.push(b);
    }
    return { pages, pageMap };
  }, [doc, blocks]);

  const matches = useMemo(() => {
    const s = new Set<string>();
    const q = debouncedQuery.trim().toLowerCase();
    if (q) {
      for (const b of blocks) {
        if (b.text.toLowerCase().includes(q)) s.add(b.id);
      }
    }
    // Always include the selectedId so it can be highlighted and scrolled to
    if (selectedId) s.add(selectedId);
    return s;
  }, [blocks, debouncedQuery, selectedId]);

  const matchedList = useMemo(() => {
    if (!debouncedQuery) return [] as TextBlock[];
    const q = debouncedQuery.trim().toLowerCase();
    return blocks.filter(b => b.text.toLowerCase().includes(q)).slice(0, 500);
  }, [blocks, debouncedQuery]);

  // Auto scroll to selected block's page
  useEffect(() => {
    if (!selectedId) return;
    const b = blocks.find(x => x.id === selectedId);
    let pageIdxToScroll: number | null = b ? b.pageIdx : null;
    if (pageIdxToScroll === null) {
      const m = /^p(\d+)b(\d+)s(\d+)$/.exec(selectedId);
      if (m) {
        pageIdxToScroll = parseInt(m[1], 10);
      }
    }
    if (pageIdxToScroll !== null) {
      const el = document.getElementById(`page-${pageIdxToScroll}`);
      if (el) el.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  }, [selectedId, blocks]);

  return (
    <div className="min-h-screen w-full bg-gray-50 text-gray-900">
      <div className="flex h-screen">
        {/* Left rail */}
        <aside className="w-[380px] border-r border-gray-200 bg-white flex flex-col overflow-y-auto">
          <div className="p-4 border-b border-gray-100">
            <h1 className="text-xl font-semibold">可溯源报告 PoC</h1>
            <p className="text-sm text-gray-500 mt-1">从 JSON 定位到源文件坐标（页码 & 坐标系）。</p>
          </div>

          <div className="p-4 border-b border-gray-100 space-y-3">
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-xs text-gray-600 mb-1">后端地址</label>
                <input className="w-full rounded border px-2 py-1 text-xs" value={backendUrl} onChange={e=>setBackendUrl(e.target.value)} />
              </div>
              <div>
                <label className="block text-xs text-gray-600 mb-1">App</label>
                <input className="w-full rounded border px-2 py-1 text-xs" value={appName} onChange={e=>setAppName(e.target.value)} />
              </div>
              <div>
                <label className="block text-xs text-gray-600 mb-1">User</label>
                <input className="w-full rounded border px-2 py-1 text-xs" value={userId} onChange={e=>setUserId(e.target.value)} />
              </div>
              <div>
                <label className="block text-xs text-gray-600 mb-1">Session</label>
                <input className="w-full rounded border px-2 py-1 text-xs" value={sessionId} onChange={e=>setSessionId(e.target.value)} />
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button className="px-3 py-2 text-sm rounded-lg bg-blue-600 text-white hover:bg-blue-700" onClick={uploadAllJsonToBackend}>上传选中文件至后端</button>
              <button className="px-3 py-2 text-sm rounded-lg bg-emerald-600 text-white hover:bg-emerald-700" onClick={runFill}>调用 Fill</button>
            </div>
          </div>

          <div className="p-4 border-b border-gray-100">
            <label className="block text-sm font-medium mb-2">导入 JSON</label>
            <div className="flex items-center gap-2">
              <input
                type="file"
                accept="application/json"
                multiple
                onChange={(e) => {
                  const fl = e.target.files;
                  if (!fl) return;
                  for (let i = 0; i < fl.length; i++) onUpload(fl[i]!);
                }}
                className="block w-full text-sm file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
            </div>
            <div className="mt-3 text-xs text-gray-500">
              <p>已加载 JSON：{Object.keys(jsonFiles).length} 个（当前统计：页 {stats.pages}，可定位文本块 {stats.blocks}）</p>
              {!!Object.keys(jsonFiles).length && (
                <div className="mt-1 space-y-1">
                  {Object.keys(jsonFiles).map(name => (
                    <div key={name} className="flex items-center gap-2">
                      <button className="text-blue-600 underline text-xs" onClick={()=>switchToJson(name)}>{name}</button>
                      <span className="text-gray-400 text-[10px]">→ {jsonNameToPdfName(name)}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="p-4 border-b border-gray-100">
            <label className="block text-sm font-medium mb-2">导入 PDF</label>
            <div className="flex items-center gap-2">
              <input
                type="file"
                accept="application/pdf"
                multiple
                onChange={async (e) => {
                  const fl = e.target.files;
                  if (!fl) return;
                  for (let i = 0; i < fl.length; i++) await onUploadPdf(fl[i]!);
                }}
                className="block w-full text-sm file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-emerald-50 file:text-emerald-700 hover:file:bg-emerald-100"
              />
            </div>
            <div className="mt-3 text-xs text-gray-500">
              <p>{pdfDoc ? `PDF 已加载：${pdfDoc.numPages} 页` : "尚未加载 PDF"}</p>
            </div>
          </div>

          <div className="p-4 border-b border-gray-100 space-y-2">
            <label className="block text-sm font-medium">Schema（JSON）</label>
            <textarea className="w-full h-40 text-xs border rounded p-2 font-mono" value={schemaText} onChange={e=>setSchemaText(e.target.value)} />
            <label className="block text-sm font-medium">补充指示（可选）</label>
            <input className="w-full text-sm border rounded p-2" value={instructions} onChange={e=>setInstructions(e.target.value)} />
          </div>

          <div className="p-4 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <label className="block text-sm font-medium">关键词搜索与定位</label>
              <button
                className="text-xs text-blue-600 hover:underline"
                onClick={() => setSearchOpen(o => !o)}
              >{searchOpen ? '收起' : '展开'}</button>
            </div>
            {searchOpen && (
              <div className="space-y-3 mt-2">
                <div>
                  <input
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    placeholder="例如：2025Q1 / 净利润 / 风险提示"
                    className="w-full rounded-xl border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <div className="mt-1 text-xs text-gray-500">匹配 {matches.size} 处</div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">按 span id 定位（格式：p{'{'}page{'}'}b{'{'}block{'}'}s{'{'}span{'}'}，0-indexed）</label>
                  <div className="flex items-center gap-2">
                    <input
                      value={spanQuery}
                      onChange={(e) => setSpanQuery(e.target.value)}
                      placeholder="例如：p17b0s1"
                      className="flex-1 rounded-xl border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500"
                    />
                    <button
                      className="px-3 py-2 text-sm rounded-lg bg-emerald-600 text-white hover:bg-emerald-700"
                      onClick={() => {
                        const raw = spanQuery.trim();
                        if (!raw) return;
                        const parsed = parseSpanId(raw);
                        if (!parsed) {
                          alert("ID 格式不正确，应为 p{page}b{block}s{span}，例如 p0b3s12");
                          return;
                        }
                        const exact = blocks.find(b => b.id === raw);
                        if (exact) { setSelectedId(exact.id); return; }
                        const prefix = `p${parsed.page}b${parsed.block}s`;
                        const candidates = blocks.filter(b => b.id.startsWith(prefix));
                        if (candidates.length > 0) {
                          let nearest = candidates[0];
                          let nearestDelta = Number.MAX_SAFE_INTEGER;
                          for (const cand of candidates) {
                            const m = /^p(\d+)b(\d+)s(\d+)$/.exec(cand.id);
                            const sIdx = m ? parseInt(m[3], 10) : 0;
                            const delta = Math.abs(sIdx - parsed.span);
                            if (delta < nearestDelta) { nearest = cand; nearestDelta = delta; }
                          }
                          setSelectedId(nearest.id); return;
                        }
                        setSelectedId(`p${parsed.page}b0s0`);
                        alert("未在当前文档中找到该 span，已滚动到对应页。");
                      }}
                    >定位</button>
                  </div>
                  <div className="mt-1 text-xs text-gray-500">示例：p0b3s12 表示第 1 页、第 4 个块中的第 13 个 span。</div>
                </div>
                <div className="flex items-center justify-between">
                  <label className="text-sm">显示全部框</label>
                  <input type="checkbox" checked={showAllBoxes} onChange={(e) => setShowAllBoxes(e.target.checked)} />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">缩放</label>
                  <input type="range" min={0.3} max={2} step={0.05} value={scale} onChange={(e) => setScale(parseFloat(e.target.value))} className="w-full" />
                  <div className="mt-1 text-xs text-gray-500">{Math.round(scale * 100)}% 宽度</div>
                </div>
              </div>
            )}
          </div>
          {/* Fill result panel moved to left rail, above keyword search */}
          <div className="p-4 border-b border-gray-100">
            <div className="text-sm font-medium mb-2">填报结果</div>
            {fillData ? (
              <div className="pr-2">
                <DataView obj={fillData} ptr="/" />
              </div>
            ) : (
              <div className="text-xs text-gray-500">暂无结果。请配置 Schema 并点击“调用 Fill”。</div>
            )}
          </div>

          <div className="flex-1 overflow-auto">
            {debouncedQuery ? (
              <ul className="divide-y divide-gray-100">
                {matchedList.map(b => (
                  <li
                    key={b.id}
                    className={`p-3 hover:bg-blue-50 cursor-pointer ${selectedId === b.id ? "bg-blue-50" : ""}`}
                    onClick={() => setSelectedId(b.id)}
                  >
                    <div className="text-xs text-gray-500">第 {b.pageIdx + 1} 页 · 坐标 [{Math.round(b.bbox[0])},{Math.round(b.bbox[1])},{Math.round(b.bbox[2])},{Math.round(b.bbox[3])}]</div>
                    <div className="text-sm line-clamp-3 mt-1" dangerouslySetInnerHTML={{
                      __html: highlight(b.text, debouncedQuery)
                    }} />
                  </li>
                ))}
              </ul>
            ) : (
              <div className="p-4 text-xs text-gray-500">
                请输入关键词进行定位。也可勾选“显示全部框”查看页面上所有可定位文本区域。
              </div>
            )}
          </div>
        </aside>

        {/* Right content: pages */}
        <main className="flex-1 overflow-auto p-6 space-y-8">
          {!doc ? (
            <div className="text-gray-500 text-sm">
              请在左侧导入 MinerU 导出的 JSON（包含 pdf_info/para_blocks/bbox/page_size 等字段），以及对应的 PDF 文件。导入后，将渲染每一页 PDF，并在其上方叠加定位框，可按关键词定位到具体坐标区域。
            </div>
          ) : (
            <div className="space-y-10">
              {(doc.pdf_info || []).sort((a, b) => (a.page_idx ?? 0) - (b.page_idx ?? 0)).map((p) => {
                const pageIdx = p.page_idx ?? 0;
                const size = (p.page_size || [595, 841]) as [number, number];
                const perPageBlocks = (pageMap.get(pageIdx) || []);
                const matchSet = new Set<string>(perPageBlocks
                  .filter(b => {
                    const q = debouncedQuery ? b.text.toLowerCase().includes(debouncedQuery.toLowerCase()) : false;
                    const sel = selectedId ? b.id === selectedId : false;
                    return q || sel;
                  })
                  .map(b => b.id)
                );
                return (
                  <PdfPageView
                    key={pageIdx}
                    pageIdx={pageIdx}
                    jsonPageSize={size}
                    perPageBlocks={perPageBlocks}
                    matches={matchSet}
                    selectedId={selectedId}
                    scale={scale}
                    showAllBoxes={showAllBoxes}
                    pdfDoc={pdfDoc}
                  />
                );
              })}
            </div>
          )}

          {/* Fill result rendering moved to left rail */}
        </main>
      </div>
    </div>
  );
}

// --- Small utils ---
function escapeHtml(s: string) {
  return s.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;");
}
function highlight(text: string, q: string) {
  const t = escapeHtml(text);
  if (!q) return t;
  try {
    const re = new RegExp(`(${q.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`, "ig");
    return t.replace(re, '<mark class="bg-yellow-200">$1</mark>');
  } catch {
    return t;
  }
}
