FROM python:3.13.5-slim AS backend-builder

WORKDIR /app

ENV PYTHONPATH='/app' \
    POETRY_VIRTUALENVS_IN_PROJECT=1

RUN python3 -m pip install poetry==2.1.2  --break-system-packages --no-cache-dir

COPY ./pyproject.toml ./poetry.lock ./
RUN poetry install --without dev --without test --no-cache --no-interaction

FROM python:3.13.5-slim AS mareo

WORKDIR /app

RUN apt-get update -y \
    && apt-get install -y curl \
    && rm -rf /var/lib/apt/lists/*

RUN groupadd -r mareo && useradd --no-log-init -r -g mareo mareo

RUN chown -R mareo:mareo /app && chmod -R 770 /app

ENV VIRTUAL_ENV=/app/.venv \
    PATH="/app/.venv/bin:$PATH" \
    PYTHONPATH='/app'

COPY --chown=mareo:mareo --chmod=770 ./frontend ./frontend
COPY --chown=mareo:mareo --chmod=770 --from=backend-builder ${VIRTUAL_ENV} ${VIRTUAL_ENV}
COPY --chown=mareo:mareo --chmod=770 ./mareo ./mareo

USER mareo

CMD ["uvicorn", "mareo.app.server:app", "--host", "0.0.0.0", "--port", "3000"]
